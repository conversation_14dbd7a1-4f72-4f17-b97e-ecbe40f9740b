import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Button, ButtonText } from '@indie-points/ui-button';
import { HStack } from '@indie-points/ui-hstack';
import { Spinner } from '@indie-points/ui-spinner';
import React from 'react';

interface CloseAccountButtonProps {
  onCloseAccount: () => void;
  isDeleting: boolean;
}

export function CloseAccountButton({
  onCloseAccount,
  isDeleting,
}: CloseAccountButtonProps) {
  return (
    <Button
      size='lg'
      onPress={onCloseAccount}
      disabled={isDeleting}
      className='w-full bg-error-600 rounded-xl border-2 border-error-800 shadow-lg'
    >
      <HStack className='items-center justify-center'>
        {isDeleting ? (
          <Spinner size='small' color='white' />
        ) : (
          <FontAwesome
            name='trash'
            size={20}
            color='white'
            style={{ marginRight: 8 }}
          />
        )}
        <ButtonText className='text-white font-semibold text-lg ml-2'>
          {isDeleting ? 'Closing Account...' : 'Close Account'}
        </ButtonText>
      </HStack>
    </Button>
  );
}
