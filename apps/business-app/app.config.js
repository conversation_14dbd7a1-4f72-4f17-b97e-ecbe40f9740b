const IS_DEV = process.env.APP_VARIANT === 'development';
const IS_PREVIEW = process.env.APP_VARIANT === 'preview';

const getUniqueIdentifier = () => {
  if (IS_DEV) {
    return 'com.indiestuart.businessapp.dev';
  }

  if (IS_PREVIEW) {
    return 'com.indiestuart.businessapp.preview';
  }

  return 'com.indiestuart.businessapp';
};

const getAppIcon = () => {
  if (IS_DEV) {
    return './assets/images/icon.dev.png';
  }

  if (IS_PREVIEW) {
    return './assets/images/icon.preview.png';
  }

  return './assets/images/icon.png';
};

const getSlug = () => {
  if (IS_DEV) {
    return 'business-app-dev';
  }

  if (IS_PREVIEW) {
    return 'business-app-preview';
  }

  return 'business-app';
};

const getProjectId = () => {
  if (IS_DEV) {
    return '056ce6be-0ab3-476b-88ad-c583723e5e6d';
  }

  if (IS_PREVIEW) {
    return '3f20bfd4-3149-4138-9612-df2614673ab6';
  }

  return '70a54468-48c4-4ce8-8cb8-461f7b939894';
};

const getUpdateUrl = () => {
  if (IS_DEV) {
    return 'https://u.expo.dev/056ce6be-0ab3-476b-88ad-c583723e5e6d';
  }

  if (IS_PREVIEW) {
    return 'https://u.expo.dev/3f20bfd4-3149-4138-9612-df2614673ab6';
  }

  return 'https://u.expo.dev/70a54468-48c4-4ce8-8cb8-461f7b939894';
};

export default ({ config }) => ({
  ...config,
  icon: getAppIcon(),
  slug: getSlug(),
  extra: {
    eas: {
      projectId: getProjectId(),
      updateUrl: getUpdateUrl(),
    },
  },
  ios: {
    ...config.ios,
    bundleIdentifier: getUniqueIdentifier(),
  },
  android: {
    ...config.android,
    package: getUniqueIdentifier(),
  },
});
