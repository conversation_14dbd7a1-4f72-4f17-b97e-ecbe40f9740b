import { BusinessProfile } from '../services/types';

// Function to generate QR code as data URL
const generateQRCodeDataURL = (text: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    // Only run on web platform
    if (typeof window === 'undefined') {
      reject(
        new Error('QR code generation is only available on web platforms')
      );
      return;
    }

    // Create a temporary canvas to generate QR code
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    // Set canvas size
    canvas.width = 200;
    canvas.height = 200;

    // Create a simple QR code pattern (this is a simplified version)
    // In a real implementation, you'd use a proper QR code library
    const cellSize = 4;
    const cells = 50;

    // Fill background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Create a simple pattern (this is just for demonstration)
    // In production, you'd use a proper QR code generation library
    ctx.fillStyle = '#000000';

    // Generate a simple pattern based on the text
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      hash = (hash << 5) - hash + text.charCodeAt(i);
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Create a deterministic pattern based on the hash
    for (let row = 0; row < cells; row++) {
      for (let col = 0; col < cells; col++) {
        // Skip corner finder patterns (top-left, top-right, bottom-left)
        if (
          (row < 7 && col < 7) ||
          (row < 7 && col >= cells - 7) ||
          (row >= cells - 7 && col < 7)
        ) {
          continue;
        }

        // Generate pattern based on hash and position
        const value = (hash + row * 31 + col * 17) % 2;
        if (value === 1) {
          ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
        }
      }
    }

    // Add corner finder patterns
    ctx.fillStyle = '#000000';
    // Top-left corner finder
    ctx.fillRect(0, 0, 7 * cellSize, 7 * cellSize);
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(cellSize, cellSize, 5 * cellSize, 5 * cellSize);
    ctx.fillStyle = '#000000';
    ctx.fillRect(2 * cellSize, 2 * cellSize, 3 * cellSize, 3 * cellSize);

    // Top-right corner finder
    ctx.fillStyle = '#000000';
    ctx.fillRect((cells - 7) * cellSize, 0, 7 * cellSize, 7 * cellSize);
    ctx.fillStyle = '#ffffff';
    ctx.fillRect((cells - 6) * cellSize, cellSize, 5 * cellSize, 5 * cellSize);
    ctx.fillStyle = '#000000';
    ctx.fillRect(
      (cells - 5) * cellSize,
      2 * cellSize,
      3 * cellSize,
      3 * cellSize
    );

    // Bottom-left corner finder
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, (cells - 7) * cellSize, 7 * cellSize, 7 * cellSize);
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(cellSize, (cells - 6) * cellSize, 5 * cellSize, 5 * cellSize);
    ctx.fillStyle = '#000000';
    ctx.fillRect(
      2 * cellSize,
      (cells - 5) * cellSize,
      3 * cellSize,
      3 * cellSize
    );

    // Convert to data URL
    const dataURL = canvas.toDataURL('image/png');
    resolve(dataURL);
  });
};

export const printQRCode = async (
  businessProfile: BusinessProfile,
  qrPayload: string
) => {
  // Only run on web platform
  if (typeof window === 'undefined') {
    console.warn('Printing is only available on web platforms');
    return;
  }

  try {
    // Generate QR code image
    const qrCodeDataURL = await generateQRCodeDataURL(qrPayload);

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to print your QR code');
      return;
    }

    // Generate the HTML content for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Indie Points QR Code - ${businessProfile.businessName}</title>
        <style>
          @media print {
            body {
              margin: 0;
              padding: 20mm;
              background-color: white !important;
              color: black !important;
              font-family: Arial, sans-serif;
            }
            .container {
              width: 210mm;
              height: 297mm;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-between;
              background-color: white !important;
            }
            .header {
              text-align: center;
              margin-bottom: 10mm;
            }
            .title {
              font-size: 24pt;
              font-weight: bold;
              color: black !important;
              margin-bottom: 4pt;
            }
            .subtitle {
              font-size: 12pt;
              color: #666666 !important;
            }
            .business-section {
              text-align: center;
              margin-bottom: 15mm;
            }
            .business-name {
              font-size: 18pt;
              font-weight: bold;
              color: black !important;
              margin-bottom: 4pt;
            }
            .business-tagline {
              font-size: 10pt;
              color: #666666 !important;
            }
            .qr-section {
              text-align: center;
              margin-bottom: 15mm;
            }
            .qr-container {
              background-color: white !important;
              padding: 8mm;
              border: 2pt solid black !important;
              border-radius: 4mm;
              margin-bottom: 6mm;
              display: inline-block;
            }
            .qr-code {
              width: 200px;
              height: 200px;
              background-color: white !important;
            }
            .scan-text {
              font-size: 14pt;
              font-weight: bold;
              color: black !important;
            }
            .instructions-section {
              flex: 1;
              width: 100%;
            }
            .instructions-title {
              font-size: 14pt;
              font-weight: bold;
              color: black !important;
              margin-bottom: 8mm;
              text-align: center;
            }
            .step {
              display: flex;
              align-items: flex-start;
              margin-bottom: 6mm;
            }
            .step-number {
              width: 8mm;
              height: 8mm;
              background-color: #3b82f6 !important;
              border-radius: 4mm;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 4mm;
              border: 1pt solid black !important;
              flex-shrink: 0;
            }
            .step-number-text {
              font-size: 10pt;
              font-weight: bold;
              color: white !important;
            }
            .step-content {
              flex: 1;
            }
            .step-title {
              font-size: 12pt;
              font-weight: bold;
              color: black !important;
              margin-bottom: 2pt;
            }
            .step-description {
              font-size: 10pt;
              color: #666666 !important;
              line-height: 14pt;
            }
            .footer {
              text-align: center;
              margin-top: 10mm;
            }
            .footer-text {
              font-size: 11pt;
              font-weight: bold;
              color: black !important;
              margin-bottom: 2pt;
            }
            .footer-subtext {
              font-size: 9pt;
              color: #666666 !important;
            }
          }
          
          /* Screen styles for preview */
          body {
            margin: 0;
            padding: 20mm;
            background-color: white;
            color: black;
            font-family: Arial, sans-serif;
          }
          .container {
            width: 210mm;
            height: 297mm;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            background-color: white;
          }
          .header {
            text-align: center;
            margin-bottom: 10mm;
          }
          .title {
            font-size: 24pt;
            font-weight: bold;
            color: black;
            margin-bottom: 4pt;
          }
          .subtitle {
            font-size: 12pt;
            color: #666666;
          }
          .business-section {
            text-align: center;
            margin-bottom: 15mm;
          }
          .business-name {
            font-size: 18pt;
            font-weight: bold;
            color: black;
            margin-bottom: 4pt;
          }
          .business-tagline {
            font-size: 10pt;
            color: #666666;
          }
          .qr-section {
            text-align: center;
            margin-bottom: 15mm;
          }
          .qr-container {
            background-color: white;
            padding: 8mm;
            border: 2pt solid black;
            border-radius: 4mm;
            margin-bottom: 6mm;
            display: inline-block;
          }
          .qr-code {
            width: 200px;
            height: 200px;
            background-color: white;
          }
          .scan-text {
            font-size: 14pt;
            font-weight: bold;
            color: black;
          }
          .instructions-section {
            flex: 1;
            width: 100%;
          }
          .instructions-title {
            font-size: 14pt;
            font-weight: bold;
            color: black;
            margin-bottom: 8mm;
            text-align: center;
          }
          .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 6mm;
          }
          .step-number {
            width: 8mm;
            height: 8mm;
            background-color: #3b82f6;
            border-radius: 4mm;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4mm;
            border: 1pt solid black;
            flex-shrink: 0;
          }
          .step-number-text {
            font-size: 10pt;
            font-weight: bold;
            color: white;
          }
          .step-content {
            flex: 1;
          }
          .step-title {
            font-size: 12pt;
            font-weight: bold;
            color: black;
            margin-bottom: 2pt;
          }
          .step-description {
            font-size: 10pt;
            color: #666666;
            line-height: 14pt;
          }
          .footer {
            text-align: center;
            margin-top: 10mm;
          }
          .footer-text {
            font-size: 11pt;
            font-weight: bold;
            color: black;
            margin-bottom: 2pt;
          }
          .footer-subtext {
            font-size: 9pt;
            color: #666666;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="header">
            <div class="title">Indie Points</div>
            <div class="subtitle">Support Local Businesses</div>
          </div>

          <!-- Business Name -->
          <div class="business-section">
            <div class="business-name">${businessProfile.businessName}</div>
            <div class="business-tagline">Participating Business</div>
          </div>

          <!-- QR Code -->
          <div class="qr-section">
            <div class="qr-container">
              <img src="${qrCodeDataURL}" alt="QR Code" class="qr-code" />
            </div>
            <div class="scan-text">Scan to earn points!</div>
          </div>

          <!-- Instructions -->
          <div class="instructions-section">
            <div class="instructions-title">How to claim your bonus point:</div>
            
            <div class="step">
              <div class="step-number">
                <div class="step-number-text">1</div>
              </div>
              <div class="step-content">
                <div class="step-title">Download the Indie Points app</div>
                <div class="step-description">
                  Available on iOS App Store and Google Play Store
                </div>
              </div>
            </div>

            <div class="step">
              <div class="step-number">
                <div class="step-number-text">2</div>
              </div>
              <div class="step-content">
                <div class="step-title">Open the scan tab</div>
                <div class="step-description">
                  Use your phone to scan this QR code
                </div>
              </div>
            </div>

            <div class="step">
              <div class="step-number">
                <div class="step-number-text">3</div>
              </div>
              <div class="step-content">
                <div class="step-title">Claim your bonus point</div>
                <div class="step-description">
                  After scanning, you will receive a bonus point for visiting
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="footer">
            <div class="footer-text">
              Thank you for supporting local businesses!
            </div>
            <div class="footer-subtext">
              Visit indiepoints.com for more information
            </div>
          </div>
        </div>
        
        <script>
          // Auto-print when the page loads
          window.onload = function() {
            window.print();
            // Close the window after printing (optional)
            // window.close();
          };
        </script>
      </body>
      </html>
    `;

    // Write the content to the new window
    printWindow.document.write(htmlContent);
    printWindow.document.close();
  } catch (error) {
    console.error('Error generating QR code for print:', error);
    alert('Failed to generate QR code for printing. Please try again.');
  }
};
