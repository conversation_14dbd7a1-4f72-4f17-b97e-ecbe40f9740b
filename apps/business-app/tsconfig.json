{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "paths": {"tailwind.config": ["./tailwind.config.js"], "@indie-points/lib": ["../../packages/lib"], "@indie-points/constants": ["../../packages/constants"], "@indie-points/contexts": ["../../packages/contexts"], "@indie-points/hooks": ["../../packages/hooks"], "@indie-points/utils": ["../../packages/utils"], "@indie-points/auth": ["../../packages/auth"], "@indie-points/forms": ["../../packages/forms"], "@indie-points/ui-button": ["../../packages/ui-button"], "@indie-points/ui-card": ["../../packages/ui-card"], "@indie-points/components": ["../../packages/components"], "@indie-points/ui-hstack": ["../../packages/ui-hstack"], "@indie-points/ui-box": ["../../packages/ui-box"], "@indie-points/ui-text": ["../../packages/ui-text"], "@indie-points/ui-vstack": ["../../packages/ui-vstack"], "@indie-points/ui-form-control": ["../../packages/ui-form-control"], "@indie-points/ui-input": ["../../packages/ui-input"], "@indie-points/ui-heading": ["../../packages/ui-heading"], "@indie-points/ui-spinner": ["../../packages/ui-spinner"], "@indie-points/ui-alert": ["../../packages/ui-alert"], "@indie-points/ui-pressable": ["../../packages/ui-pressable"], "@indie-points/ui-refresh-control": ["../../packages/ui-refresh-control"], "@indie-points/ui-gluestack-ui-provider": ["../../packages/ui-gluestack-ui-provider"], "@indie-points/ui-center": ["../../packages/ui-center"], "@indie-points/ui-slider": ["../../packages/ui-slider"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"]}