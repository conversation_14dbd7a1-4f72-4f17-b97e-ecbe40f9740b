import { useAuth } from '@indie-points/contexts';
import { act, renderHook } from '@testing-library/react-native';
import * as Haptics from 'expo-haptics';
import { Alert } from 'react-native';

import { useSettingsScreenData } from '../useSettingsScreenData';

// Mock the auth context
jest.mock('@indie-points/contexts', () => ({
  useAuth: jest.fn(),
}));

// Mock expo-router
jest.mock('expo-router', () => ({
  router: {
    replace: jest.fn(),
  },
}));

// Mock expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Medium: 'medium',
    Heavy: 'heavy',
  },
  NotificationFeedbackType: {
    Success: 'success',
  },
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;

describe('useSettingsScreenData', () => {
  const mockSignOut = jest.fn();
  const mockDeleteAccount = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseAuth.mockReturnValue({
      user: { id: 'test-user-id', email: '<EMAIL>' },
      signOut: mockSignOut,
      deleteAccount: mockDeleteAccount,
      loading: false,
    } as any);
  });

  describe('handleDeleteAccount', () => {
    it('should show initial confirmation dialog', () => {
      const { result } = renderHook(() => useSettingsScreenData());

      act(() => {
        result.current.handleDeleteAccount();
      });

      expect(Haptics.impactAsync).toHaveBeenCalledWith('medium');
      expect(Alert.alert).toHaveBeenCalledWith(
        'Close Account',
        'Are you sure you want to close your account? This action cannot be undone.',
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel', style: 'cancel' }),
          expect.objectContaining({ text: 'Continue', style: 'destructive' }),
        ])
      );
    });

    it('should handle successful account deletion', async () => {
      mockDeleteAccount.mockResolvedValue({ error: null });

      const { result } = renderHook(() => useSettingsScreenData());

      // Simulate the full flow by calling the final confirmation directly
      act(() => {
        result.current.handleDeleteAccount();
      });

      // Get the final confirmation callback from the Alert.alert call
      const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
      const finalConfirmationCallback = alertCall[2][1].onPress;

      await act(async () => {
        await finalConfirmationCallback();
      });

      expect(mockDeleteAccount).toHaveBeenCalled();
      expect(Haptics.notificationAsync).toHaveBeenCalledWith('success');
      expect(result.current.deletingAccount).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle account deletion error', async () => {
      const mockError = { message: 'Deletion failed' };
      mockDeleteAccount.mockResolvedValue({ error: mockError });

      const { result } = renderHook(() => useSettingsScreenData());

      act(() => {
        result.current.handleDeleteAccount();
      });

      // Get the final confirmation callback
      const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
      const finalConfirmationCallback = alertCall[2][1].onPress;

      await act(async () => {
        await finalConfirmationCallback();
      });

      expect(result.current.error).toBe('Deletion failed');
      expect(result.current.deletingAccount).toBe(false);
    });

    it('should handle unexpected errors during deletion', async () => {
      mockDeleteAccount.mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useSettingsScreenData());

      act(() => {
        result.current.handleDeleteAccount();
      });

      // Get the final confirmation callback
      const alertCall = (Alert.alert as jest.Mock).mock.calls[0];
      const finalConfirmationCallback = alertCall[2][1].onPress;

      await act(async () => {
        await finalConfirmationCallback();
      });

      expect(result.current.error).toBe(
        'Failed to delete account. Please try again.'
      );
      expect(result.current.deletingAccount).toBe(false);
    });
  });

  describe('handleSignOut', () => {
    it('should show sign out confirmation dialog', () => {
      const { result } = renderHook(() => useSettingsScreenData());

      act(() => {
        result.current.handleSignOut();
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Sign out',
        'Are you sure you want to sign out?',
        expect.arrayContaining([
          expect.objectContaining({ text: 'Cancel', style: 'cancel' }),
          expect.objectContaining({ text: 'Sign out', style: 'destructive' }),
        ])
      );
    });
  });

  describe('state management', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => useSettingsScreenData());

      expect(result.current.deletingAccount).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.user).toEqual({
        id: 'test-user-id',
        email: '<EMAIL>',
      });
      expect(result.current.loading).toBe(false);
      expect(typeof result.current.handleSignOut).toBe('function');
      expect(typeof result.current.handleDeleteAccount).toBe('function');
    });
  });
});
