import { GradientBar } from '@indie-points/auth';
import { Heading } from '@indie-points/ui-heading';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface HeaderSectionProps {
  title?: string;
}

export function HeaderSection({ title = 'Scan' }: HeaderSectionProps) {
  return (
    <VStack space='lg' className='px-6 pt-6 pb-6'>
      <Heading size='3xl' className='text-typography-900 font-bold'>
        {title}
      </Heading>
      <GradientBar />
    </VStack>
  );
}
