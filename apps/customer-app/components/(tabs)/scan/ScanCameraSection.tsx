import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { Spinner } from '@indie-points/ui-spinner';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import { CameraView } from 'expo-camera';
import React from 'react';

interface ScanCameraSectionProps {
  permission: any;
  loading: boolean;
  error: string | null;
  success: string | null;
  scannedData: any;
  scanCompleted: boolean;
  facing: any;
  handleBarCodeScanned: (data: any) => void;
  reset: () => void;
}

export function ScanCameraSection({
  permission,
  loading,
  scannedData,
  scanCompleted,
  facing,
  handleBarCodeScanned,
  reset,
}: ScanCameraSectionProps) {
  return (
    <VStack space='lg' className='items-center'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        Scan a business QR code
      </Heading>
      <Box className='w-full aspect-square bg-black rounded-2xl border-4 border-typography-900 items-center justify-center shadow-lg overflow-hidden'>
        {!permission ? (
          <Text size='md' className='text-white'>
            Requesting camera permission...
          </Text>
        ) : !permission.granted ? (
          <VStack space='md' className='items-center'>
            <FontAwesome name='camera' size={64} color='#fff' />
            <Text size='md' className='text-white text-center'>
              Camera access is required to scan business QR codes.
            </Text>
          </VStack>
        ) : loading ? (
          <VStack space='md' className='items-center'>
            <Spinner size='large' color='#fff' />
            <Text size='md' className='text-white text-center'>
              Processing visit...
            </Text>
            {scannedData && (
              <Text size='sm' className='text-white text-center'>
                {scannedData.businessName}
              </Text>
            )}
          </VStack>
        ) : scanCompleted ? (
          <VStack space='lg' className='items-center justify-center flex-1'>
            <FontAwesome name='check-circle' size={64} color='#22c55e' />
            <Text size='lg' className='text-white text-center font-semibold'>
              Scan complete!
            </Text>
            <Button
              size='lg'
              action='primary'
              className='mt-4 bg-primary-500'
              onPress={reset}
            >
              <ButtonText>Scan again</ButtonText>
            </Button>
          </VStack>
        ) : (
          <CameraView
            style={{ width: '100%', aspectRatio: 1 }}
            facing={facing}
            ratio='1:1'
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
            onBarcodeScanned={handleBarCodeScanned}
          />
        )}
      </Box>
    </VStack>
  );
}
