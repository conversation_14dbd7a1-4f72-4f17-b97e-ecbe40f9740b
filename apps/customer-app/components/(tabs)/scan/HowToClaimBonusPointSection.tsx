import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

export function HowToClaimBonusPointSection() {
  return (
    <VStack space='lg'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        How to claim a bonus point
      </Heading>
      {/* Step 1 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            1
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Visit a participating business
          </Text>
          <Text size='sm' className='text-typography-600'>
            Look for the Indie Points logo at local businesses
          </Text>
        </VStack>
      </HStack>
      {/* Step 2 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            2
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Open the scan tab
          </Text>
          <Text size='sm' className='text-typography-600'>
            Use your phone to scan the QR code of the business
          </Text>
        </VStack>
      </HStack>
      {/* Step 3 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            3
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Claim your bonus point
          </Text>
          <Text size='sm' className='text-typography-600'>
            After scanning, you will receive a bonus point for visiting
          </Text>
        </VStack>
      </HStack>
    </VStack>
  );
}
