import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Button, ButtonText } from '@indie-points/ui-button';
import { HStack } from '@indie-points/ui-hstack';
import React from 'react';

export function SignOutButton({ onSignOut }: { onSignOut: () => void }) {
  return (
    <Button
      size='lg'
      onPress={onSignOut}
      className='w-full bg-error-500 rounded-xl border-2 border-error-700 shadow-lg'
    >
      <HStack className='items-center justify-center'>
        <FontAwesome
          name='sign-out'
          size={20}
          color='white'
          style={{ marginRight: 8 }}
        />
        <ButtonText className='text-white font-semibold text-lg'>
          Sign out
        </ButtonText>
      </HStack>
    </Button>
  );
}
