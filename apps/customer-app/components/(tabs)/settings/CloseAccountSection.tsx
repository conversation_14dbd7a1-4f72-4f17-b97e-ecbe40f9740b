import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { CloseAccountButton } from './CloseAccountButton';

interface CloseAccountSectionProps {
  onCloseAccount: () => void;
  isDeleting: boolean;
  error: string | null;
}

export function CloseAccountSection({
  onCloseAccount,
  isDeleting,
  error,
}: CloseAccountSectionProps) {
  return (
    <Box className='bg-error-50 border-2 border-error-200 rounded-xl p-6'>
      <VStack space='lg'>
        {/* Header */}
        <HStack className='items-center'>
          <FontAwesome name='warning' size={24} color='#dc2626' />
          <Text className='text-error-700 font-bold text-xl ml-3'>
            Close Account
          </Text>
        </HStack>

        {/* Warning Text */}
        <VStack space='md'>
          <Text className='text-error-700 font-medium text-base'>
            ⚠️ This action cannot be undone
          </Text>

          <Text className='text-error-600 text-sm leading-relaxed'>
            Closing your account will permanently delete:
          </Text>

          <VStack space='xs' className='ml-4'>
            <Text className='text-error-600 text-sm'>
              • Your customer profile and settings
            </Text>
            <Text className='text-error-600 text-sm'>
              • All your points and rewards
            </Text>
            <Text className='text-error-600 text-sm'>
              • Your complete transaction history
            </Text>
            <Text className='text-error-600 text-sm'>
              • Your login credentials
            </Text>
          </VStack>

          <Text className='text-error-700 font-medium text-sm mt-2'>
            You will lose access to all businesses and rewards in your account.
          </Text>
        </VStack>

        {/* Error Message */}
        {error && (
          <Box className='bg-error-100 border border-error-300 rounded-lg p-3'>
            <Text className='text-error-700 text-sm font-medium'>{error}</Text>
          </Box>
        )}

        {/* Close Account Button */}
        <CloseAccountButton
          onCloseAccount={onCloseAccount}
          isDeleting={isDeleting}
        />
      </VStack>
    </Box>
  );
}
