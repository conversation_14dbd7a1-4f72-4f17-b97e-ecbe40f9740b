import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

export function UserProfileCard({
  user,
}: {
  user: { email?: string; created_at?: string } | null;
}) {
  // Format the created_at date if available
  const memberSince = user?.created_at
    ? new Date(user.created_at).toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    : 'Unknown';
  return (
    <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
      <HStack space='md' className='items-center'>
        <Box className='w-16 h-16 bg-primary-500 rounded-2xl items-center justify-center'>
          <FontAwesome name='user' size={24} color='white' />
        </Box>
        <VStack className='flex-1'>
          <Text size='lg' className='text-typography-900 font-semibold'>
            {user?.email || 'Not signed in'}
          </Text>
          <Text size='sm' className='text-typography-600'>
            Member since {memberSince}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
}
