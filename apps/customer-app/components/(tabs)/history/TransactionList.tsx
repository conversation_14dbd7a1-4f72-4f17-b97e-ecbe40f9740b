import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { ActivityIndicator } from 'react-native';

import { TransactionListItem } from './TransactionListItem';
import { CustomerTransaction } from '../../../services/types';

interface TransactionListProps {
  transactions: CustomerTransaction[];
  loading: boolean;
  error?: string;
  loadingMore?: boolean;
  hasMore?: boolean;
  onTransactionPress: (transaction: CustomerTransaction) => void;
}

export const TransactionList: React.FC<TransactionListProps> = ({
  transactions,
  loading,
  error,
  loadingMore,
  hasMore,
  onTransactionPress,
}) => {
  if (loading) {
    return (
      <Box className='flex-1 items-center justify-center py-12'>
        <ActivityIndicator size='large' color='#3B82F6' />
        <Text size='md' className='text-typography-600 mt-4'>
          Loading history...
        </Text>
      </Box>
    );
  }
  if (error) {
    return (
      <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
        <HStack space='md' className='items-center'>
          <FontAwesome name='exclamation-triangle' size={20} color='#EF4444' />
          <VStack className='flex-1'>
            <Text size='md' className='text-error-700 font-semibold'>
              Error loading data
            </Text>
            <Text size='sm' className='text-error-600'>
              {error}
            </Text>
          </VStack>
        </HStack>
      </Box>
    );
  }
  if (transactions.length === 0) {
    return (
      <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
        <FontAwesome name='history' size={40} color='#9CA3AF' />
        <Text size='md' className='text-typography-600 mt-4 text-center'>
          No transactions found
        </Text>
        <Text size='sm' className='text-typography-500 mt-2 text-center'>
          Your transaction history will appear here once you start earning
          points
        </Text>
      </Box>
    );
  }
  return (
    <VStack space='md'>
      {transactions.map(tx => (
        <TransactionListItem
          key={tx.id}
          transaction={tx}
          onPress={onTransactionPress}
        />
      ))}
      {loadingMore && (
        <Box className='py-4 items-center'>
          <ActivityIndicator size='small' color='#3B82F6' />
          <Text size='sm' className='text-typography-600 mt-2'>
            Loading more transactions...
          </Text>
        </Box>
      )}
      {!hasMore && transactions.length > 0 && (
        <Box className='py-4 items-center'>
          <Text size='sm' className='text-typography-500'>
            You have reached the end of your transaction history
          </Text>
        </Box>
      )}
    </VStack>
  );
};
