import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import dayjs from 'dayjs';
import React from 'react';

import { CustomerBusinessSummary } from '../../../services/types';

interface BusinessListItemProps {
  business: CustomerBusinessSummary;
  onPress: (business: CustomerBusinessSummary) => void;
}

export const BusinessListItem: React.FC<BusinessListItemProps> = ({
  business,
  onPress,
}) => (
  <Pressable
    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4 active:bg-background-50'
    onPress={() => onPress(business)}
  >
    <HStack space='md' className='items-center'>
      <Box className='w-12 h-12 bg-primary-500 border-2 border-primary-700 rounded-xl items-center justify-center'>
        <FontAwesome name='building' size={20} color='white' />
      </Box>
      <VStack className='flex-1'>
        <Text size='md' className='text-typography-900 font-semibold'>
          {business.name}
        </Text>
        <Text size='sm' className='text-typography-600'>
          {business.category}
        </Text>
        <Text size='xs' className='text-typography-500'>
          Last visit: {dayjs(business.lastVisit).format('DD MMM YYYY, HH:mm')}
        </Text>
      </VStack>
      <VStack className='items-end'>
        <Text size='lg' className='text-primary-500 font-bold'>
          {business.points} pts
        </Text>
        <FontAwesome name='chevron-right' size={12} color='#9CA3AF' />
      </VStack>
    </HStack>
  </Pressable>
);
