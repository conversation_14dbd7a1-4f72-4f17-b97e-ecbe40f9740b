import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { ActivityIndicator } from 'react-native';

import { BusinessListItem } from './BusinessListItem';
import { CustomerBusinessSummary } from '../../../services/types';

interface BusinessListProps {
  businesses: CustomerBusinessSummary[];
  loading: boolean;
  error?: string;
  onBusinessPress: (business: CustomerBusinessSummary) => void;
}

export const BusinessList: React.FC<BusinessListProps> = ({
  businesses,
  loading,
  error,
  onBusinessPress,
}) => {
  if (loading) {
    return (
      <Box className='flex-1 items-center justify-center py-12'>
        <ActivityIndicator size='large' color='#3B82F6' />
        <Text size='md' className='text-typography-600 mt-4'>
          Loading businesses...
        </Text>
      </Box>
    );
  }
  if (error) {
    return (
      <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
        <HStack space='md' className='items-center'>
          <FontAwesome name='exclamation-triangle' size={20} color='#EF4444' />
          <VStack className='flex-1'>
            <Text size='md' className='text-error-700 font-semibold'>
              Error loading data
            </Text>
            <Text size='sm' className='text-error-600'>
              {error}
            </Text>
          </VStack>
        </HStack>
      </Box>
    );
  }
  if (businesses.length === 0) {
    return (
      <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
        <FontAwesome name='building' size={40} color='#9CA3AF' />
        <Text size='md' className='text-typography-600 mt-4 text-center'>
          No businesses found
        </Text>
        <Text size='sm' className='text-typography-500 mt-2 text-center'>
          Businesses you visit will appear here
        </Text>
      </Box>
    );
  }
  return (
    <VStack space='md'>
      {businesses.map(business => (
        <BusinessListItem
          key={business.id}
          business={business}
          onPress={onBusinessPress}
        />
      ))}
    </VStack>
  );
};
