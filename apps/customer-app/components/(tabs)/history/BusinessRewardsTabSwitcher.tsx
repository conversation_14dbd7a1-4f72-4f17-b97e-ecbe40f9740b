import React from 'react';

import { TabSwitcher } from './TabSwitcher';

interface BusinessRewardsTabSwitcherProps {
  activeTab: string;
  onTabChange: (key: string) => void;
}

export const BusinessRewardsTabSwitcher: React.FC<
  BusinessRewardsTabSwitcherProps
> = ({ activeTab, onTabChange }) => (
  <TabSwitcher
    tabs={[
      { key: 'rewards', label: 'Rewards' },
      { key: 'transactions', label: 'Transactions' },
    ]}
    activeTab={activeTab}
    onTabChange={onTabChange}
  />
);
