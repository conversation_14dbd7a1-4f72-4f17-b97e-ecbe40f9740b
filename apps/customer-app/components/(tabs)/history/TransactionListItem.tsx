import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import dayjs from 'dayjs';
import React from 'react';

import { CustomerTransaction } from '../../../services/types';

interface TransactionListItemProps {
  transaction: CustomerTransaction;
  onPress: (transaction: CustomerTransaction) => void;
}

export const TransactionListItem: React.FC<TransactionListItemProps> = ({
  transaction,
  onPress,
}) => (
  <Pressable
    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4 active:bg-background-50'
    onPress={() => onPress(transaction)}
  >
    <HStack space='md' className='items-center'>
      <Box
        className={`w-12 h-12 rounded-xl items-center justify-center border-2 ${
          transaction.type === 'purchase'
            ? 'bg-primary-500 border-primary-700'
            : transaction.type === 'redemption'
              ? 'bg-error-500 border-error-700'
              : 'bg-secondary-500 border-secondary-700'
        }`}
      >
        <FontAwesome
          name={
            transaction.type === 'purchase'
              ? 'shopping-bag'
              : transaction.type === 'redemption'
                ? 'gift'
                : 'eye'
          }
          size={20}
          color='white'
        />
      </Box>
      <VStack className='flex-1'>
        <Text size='md' className='text-typography-900 font-semibold'>
          {transaction.businessName}
        </Text>
        <Text size='sm' className='text-typography-600'>
          {transaction.type === 'purchase'
            ? 'Purchase'
            : transaction.type === 'redemption'
              ? 'Redemption'
              : 'Visit'}{' '}
          • {transaction.businessCategory}
        </Text>
        <Text size='xs' className='text-typography-500'>
          {dayjs(transaction.date).format('DD MMM YYYY, HH:mm')}
        </Text>
      </VStack>
      <VStack className='items-end'>
        <Text
          size='lg'
          className={`font-bold ${
            transaction.type === 'redemption'
              ? 'text-error-500'
              : 'text-primary-500'
          }`}
        >
          {transaction.type === 'redemption'
            ? `-${transaction.pointsRedeemed ?? 0} pts`
            : `${transaction.pointsEarned > 0 ? '+' : ''}${transaction.pointsEarned} pts`}
        </Text>
        <FontAwesome name='chevron-right' size={12} color='#9CA3AF' />
      </VStack>
    </HStack>
  </Pressable>
);
