import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

interface HeaderProps {
  title: string;
  category?: string;
  showBack?: boolean;
  onBack?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  category,
  showBack,
  onBack,
}) => (
  <HStack space='md' className='items-center'>
    {showBack && (
      <Pressable
        className='w-10 h-10 bg-primary-500 rounded-xl items-center justify-center'
        onPress={onBack}
      >
        <FontAwesome name='arrow-left' size={16} color='white' />
      </Pressable>
    )}
    <VStack className='flex-1'>
      <Heading size='3xl' className='text-typography-900 font-bold'>
        {title}
      </Heading>
      {category && (
        <Text size='md' className='text-typography-600'>
          {category}
        </Text>
      )}
    </VStack>
  </HStack>
);
