import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { ActivityIndicator } from 'react-native';

import { RewardListItem } from './RewardListItem';
import { CustomerRewardEligibility } from '../../../services/types';

interface RewardsListProps {
  rewards: CustomerRewardEligibility[];
  loading: boolean;
  error?: string;
}

export const RewardsList: React.FC<RewardsListProps> = ({
  rewards,
  loading,
  error,
}) => {
  if (loading) {
    return (
      <Box className='flex-1 items-center justify-center py-12'>
        <ActivityIndicator size='large' color='#3B82F6' />
        <Text size='md' className='text-typography-600 mt-4'>
          Loading rewards...
        </Text>
      </Box>
    );
  }
  if (error) {
    return (
      <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
        <HStack space='md' className='items-center'>
          <FontAwesome name='exclamation-triangle' size={20} color='#EF4444' />
          <VStack className='flex-1'>
            <Text size='md' className='text-error-700 font-semibold'>
              Error loading rewards
            </Text>
            <Text size='sm' className='text-error-600'>
              {error}
            </Text>
          </VStack>
        </HStack>
      </Box>
    );
  }
  if (rewards.length === 0) {
    return (
      <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
        <FontAwesome name='gift' size={40} color='#9CA3AF' />
        <Text size='md' className='text-typography-600 mt-4 text-center'>
          No rewards available
        </Text>
        <Text size='sm' className='text-typography-500 mt-2 text-center'>
          This business has not configured any rewards yet
        </Text>
      </Box>
    );
  }
  return (
    <VStack space='md'>
      {[...rewards]
        .sort((a, b) => b.reward.pointsRequired - a.reward.pointsRequired)
        .map(reward => (
          <RewardListItem key={reward.reward.id} reward={reward} />
        ))}
    </VStack>
  );
};
