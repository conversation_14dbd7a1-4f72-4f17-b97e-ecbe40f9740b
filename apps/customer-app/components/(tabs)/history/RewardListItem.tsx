import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

import { CustomerRewardEligibility } from '../../../services/types';

interface RewardListItemProps {
  reward: CustomerRewardEligibility;
}

export const RewardListItem: React.FC<RewardListItemProps> = ({ reward }) => (
  <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'>
    <HStack space='md' className='items-center'>
      <Box
        className={`w-12 h-12 rounded-xl items-center justify-center border-2 ${
          reward.isEligible
            ? 'bg-primary-500 border-primary-700'
            : 'bg-secondary-500 border-secondary-700'
        }`}
      >
        <FontAwesome name='gift' size={20} color='white' />
      </Box>
      <VStack className='flex-1'>
        <Text size='md' className='text-typography-900 font-semibold'>
          {reward.reward.title}
        </Text>
        <Text size='sm' className='text-typography-600'>
          {reward.reward.description}
        </Text>
        <Text size='xs' className='text-typography-500'>
          {reward.reward.pointsRequired} points required
          {!reward.isEligible && (
            <Text size='xs' className='text-typography-500'>
              {' '}
              (need {reward.pointsNeeded} more)
            </Text>
          )}
        </Text>
      </VStack>
    </HStack>
  </Box>
);
