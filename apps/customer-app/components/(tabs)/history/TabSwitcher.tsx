import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { Text } from '@indie-points/ui-text';
import * as Haptics from 'expo-haptics';
import React from 'react';

interface TabSwitcherProps {
  tabs: { key: string; label: string }[];
  activeTab: string;
  onTabChange: (key: string) => void;
}

export const TabSwitcher: React.FC<TabSwitcherProps> = ({
  tabs,
  activeTab,
  onTabChange,
}) => (
  <HStack className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden'>
    {tabs.map(tab => (
      <Pressable
        key={tab.key}
        className={`flex-1 py-3 px-6 ${activeTab === tab.key ? 'bg-primary-500' : 'bg-white'}`}
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          onTabChange(tab.key);
        }}
      >
        <Text
          size='md'
          className={`font-semibold text-center ${activeTab === tab.key ? 'text-white' : 'text-typography-900'}`}
        >
          {tab.label}
        </Text>
      </Pressable>
    ))}
  </HStack>
);
