import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Box } from '@indie-points/ui-box';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import QRCode from 'react-native-qrcode-svg';

interface LoyaltyCardSectionProps {
  loyaltyQr: {
    qrWidth: number;
    qrValue: string;
    setQrWidth: (width: number) => void;
    regenerate: () => void;
  };
}

export function LoyaltyCardSection({ loyaltyQr }: LoyaltyCardSectionProps) {
  return (
    <VStack space='lg' className='items-center'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        Your loyalty card
      </Heading>
      <Box
        className='w-full max-w-md aspect-square bg-white rounded-2xl border-4 p-4 border-typography-900 items-center justify-center shadow-lg'
        onLayout={event => {
          const width = event.nativeEvent.layout.width;
          loyaltyQr.setQrWidth(width);
        }}
      >
        <VStack space='md' className='items-center w-full h-full'>
          {loyaltyQr.qrWidth > 0 ? (
            <QRCode
              value={loyaltyQr.qrValue}
              size={Math.max(loyaltyQr.qrWidth - 8 - 32, 0)}
              logo={require('../../../assets/images/icon.png')}
              logoSize={Math.max((loyaltyQr.qrWidth - 8 - 32) * 0.2, 32)}
              logoBackgroundColor='transparent'
            />
          ) : (
            <FontAwesome name='qrcode' size={120} color='#000' />
          )}
        </VStack>
      </Box>
      <VStack space='md' className='w-full'>
        <Button
          size='lg'
          className='w-full bg-primary-500 rounded-xl border-2 border-primary-700 shadow-lg'
          onPress={loyaltyQr.regenerate}
        >
          <HStack space='sm' className='items-center'>
            <FontAwesome name='refresh' size={16} color='white' />
            <ButtonText className='text-white font-semibold'>
              Regenerate
            </ButtonText>
          </HStack>
        </Button>
      </VStack>
    </VStack>
  );
}
