import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';

export function HowToEarnPointsSection() {
  return (
    <VStack space='lg'>
      <Heading size='xl' className='text-typography-900 font-semibold'>
        How to earn points
      </Heading>
      {/* Step 1 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-primary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            1
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Visit a participating business
          </Text>
          <Text size='sm' className='text-typography-600'>
            Look for the Indie Points logo at local businesses
          </Text>
        </VStack>
      </HStack>
      {/* Step 2 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-secondary-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            2
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Show your QR code
          </Text>
          <Text size='sm' className='text-typography-600'>
            Let the business scan your unique QR code before or after purchase
          </Text>
        </VStack>
      </HStack>
      {/* Step 3 */}
      <HStack space='md' className='items-start'>
        <Box className='w-8 h-8 bg-error-500 rounded-full items-center justify-center border-2 border-black'>
          <Text size='md' className='text-white font-bold'>
            3
          </Text>
        </Box>
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            Earn points automatically
          </Text>
          <Text size='sm' className='text-typography-600'>
            Get 1 point for every £1 spent at participating businesses
          </Text>
        </VStack>
      </HStack>
    </VStack>
  );
}
