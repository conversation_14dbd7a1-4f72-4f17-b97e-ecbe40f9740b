import { GradientBar } from '@indie-points/auth';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import { getTimeBasedGreeting } from '@indie-points/utils';
import React from 'react';

export const HeaderSection = () => (
  <VStack space='lg' className='px-6 pt-6 pb-6'>
    {/* Title */}
    <Heading size='3xl' className='text-typography-900 font-bold'>
      Home
    </Heading>
    {/* Colored divider line */}
    <GradientBar />
    {/* Greeting */}
    <VStack space='xs' className='items-center'>
      <Heading
        size='xl'
        className='text-typography-900 font-semibold text-center'
      >
        {getTimeBasedGreeting()}.
      </Heading>
      <Heading
        size='xl'
        className='text-typography-900 font-semibold text-center'
      >
        Welcome to Indie Points!
      </Heading>
      <Text size='md' className='text-typography-600 text-center'>
        Here is your points summary.
      </Text>
    </VStack>
  </VStack>
);
