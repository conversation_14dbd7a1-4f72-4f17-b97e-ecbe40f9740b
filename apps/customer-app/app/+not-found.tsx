import { Center } from '@indie-points/ui-center';
import { Text } from '@indie-points/ui-text';
import { Link, Stack } from 'expo-router';
import React from 'react';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <Center className='flex-1'>
        <Text className='text-secondary-200'>This screen does not exist.</Text>

        <Link href='/' style={{ marginTop: 10 }}>
          <Text className='text-primary-500'>Go to home screen!</Text>
        </Link>
      </Center>
    </>
  );
}
