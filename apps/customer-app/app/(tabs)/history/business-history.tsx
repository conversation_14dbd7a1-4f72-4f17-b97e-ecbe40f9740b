import { GradientBar } from '@indie-points/auth';
import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import { ScrollView } from 'react-native';

import { BusinessRewardsTabSwitcher } from '../../../components/(tabs)/history/BusinessRewardsTabSwitcher';
import { Header } from '../../../components/(tabs)/history/Header';
import { RewardsList } from '../../../components/(tabs)/history/RewardsList';
import { TransactionList } from '../../../components/(tabs)/history/TransactionList';
import { useBusinessHistoryScreenData } from '../../../hooks/composite/useBusinessHistoryScreenData';

export default function BusinessHistory() {
  const router = useRouter();
  const { businessId, businessName, businessCategory } = useLocalSearchParams<{
    businessId: string;
    businessName: string;
    businessCategory: string;
  }>();

  const {
    activeTab,
    setActiveTab,
    transactionData,
    rewardsData,
    loading,
    error,
    refreshing,
    refresh,
    loadMoreTransactions,
    hasMoreTransactions,
    loadingMoreTransactions,
    loadingRewards,
    rewardsError,
  } = useBusinessHistoryScreenData(businessId);

  const handleBackPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.back();
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={refresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;

          if (
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - paddingToBottom
          ) {
            if (activeTab === 'transactions') {
              loadMoreTransactions();
            }
          }
        }}
        scrollEventThrottle={400}
      >
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          <Header
            title={businessName || 'Business History'}
            category={businessCategory}
            showBack
            onBack={handleBackPress}
          />
          <GradientBar />
        </VStack>
        <Box className='px-6 pb-4'>
          <BusinessRewardsTabSwitcher
            activeTab={activeTab}
            onTabChange={key => setActiveTab(key as 'rewards' | 'transactions')}
          />
        </Box>
        <Box className='px-6 pb-8'>
          {activeTab === 'transactions' ? (
            <TransactionList
              transactions={transactionData}
              loading={loading && !refreshing}
              error={error || undefined}
              loadingMore={loadingMoreTransactions}
              hasMore={hasMoreTransactions}
              onTransactionPress={() => {}}
            />
          ) : (
            <RewardsList
              rewards={rewardsData}
              loading={loadingRewards}
              error={rewardsError || undefined}
            />
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
