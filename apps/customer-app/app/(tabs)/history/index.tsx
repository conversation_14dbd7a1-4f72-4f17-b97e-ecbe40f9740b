import { GradientBar } from '@indie-points/auth';
import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React from 'react';
import { ScrollView } from 'react-native';

import { BusinessList } from '../../../components/(tabs)/history/BusinessList';
import { Header } from '../../../components/(tabs)/history/Header';
import { TabSwitcher } from '../../../components/(tabs)/history/TabSwitcher';
import { TransactionList } from '../../../components/(tabs)/history/TransactionList';
import { useHistoryScreenData } from '../../../hooks/composite/useHistoryScreenData';
import {
  CustomerBusinessSummary,
  CustomerTransaction,
} from '../../../services/types';

export default function History() {
  const router = useRouter();
  const {
    activeTab,
    setActiveTab,
    transactionData,
    businessData,
    loading,
    error,
    refreshing,
    refresh,
    loadMoreTransactions,
    hasMoreTransactions,
    loadingMoreTransactions,
  } = useHistoryScreenData();

  const handleTransactionPress = (transaction: CustomerTransaction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push({
      pathname: '/history/business-history',
      params: {
        businessId: transaction.businessId.toString(),
        businessName: transaction.businessName,
        businessCategory: transaction.businessCategory,
      },
    });
  };

  const handleBusinessPress = (business: CustomerBusinessSummary) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push({
      pathname: '/history/business-history',
      params: {
        businessId: business.id.toString(),
        businessName: business.name,
        businessCategory: business.category,
      },
    });
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={refresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;

          if (
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - paddingToBottom
          ) {
            if (activeTab === 'transactions') {
              loadMoreTransactions();
            }
          }
        }}
        scrollEventThrottle={400}
      >
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          <Header title='History' />
          <GradientBar />
          <TabSwitcher
            tabs={[
              { key: 'businesses', label: 'Businesses' },
              { key: 'transactions', label: 'Transactions' },
            ]}
            activeTab={activeTab}
            onTabChange={key =>
              setActiveTab(key as 'businesses' | 'transactions')
            }
          />
        </VStack>
        <Box className='px-6 pb-8'>
          {activeTab === 'transactions' ? (
            <TransactionList
              transactions={transactionData}
              loading={loading && !refreshing}
              error={error || undefined}
              loadingMore={loadingMoreTransactions}
              hasMore={hasMoreTransactions}
              onTransactionPress={handleTransactionPress}
            />
          ) : (
            <BusinessList
              businesses={businessData}
              loading={loading && !refreshing}
              error={error || undefined}
              onBusinessPress={handleBusinessPress}
            />
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
