import { Alert, AlertText } from '@indie-points/ui-alert';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import React, { useEffect } from 'react';
import { ScrollView } from 'react-native';

import { HeaderSection } from '../../components/(tabs)/scan/HeaderSection';
import { HowToClaimBonusPointSection } from '../../components/(tabs)/scan/HowToClaimBonusPointSection';
import { ScanCameraSection } from '../../components/(tabs)/scan/ScanCameraSection';
import { useScanScreenData } from '../../hooks/composite/useScanScreenData';

export default function Scan() {
  const {
    facing,
    permission,
    requestPermission,
    loading,
    error,
    success,
    scannedData,
    scanCompleted,
    handleBarCodeScanned,
    reset,
  } = useScanScreenData();

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        <HeaderSection />
        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            {/* Feedback Messages */}
            {success && (
              <Alert action='success' variant='solid'>
                <AlertText>{success}</AlertText>
              </Alert>
            )}
            {error && (
              <Alert action='error' variant='solid'>
                <AlertText>{error}</AlertText>
              </Alert>
            )}
            <ScanCameraSection
              permission={permission}
              loading={loading}
              error={error}
              success={success}
              scannedData={scannedData}
              scanCompleted={scanCompleted}
              facing={facing}
              handleBarCodeScanned={handleBarCodeScanned}
              reset={reset}
            />
            <HowToClaimBonusPointSection />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
