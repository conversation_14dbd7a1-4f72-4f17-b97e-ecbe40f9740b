import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { VStack } from '@indie-points/ui-vstack';
import React from 'react';
import { ScrollView } from 'react-native';

import { HeaderSection } from '../../components/(tabs)/points/HeaderSection';
import { HowToEarnPointsSection } from '../../components/(tabs)/points/HowToEarnPointsSection';
import { LoyaltyCardSection } from '../../components/(tabs)/points/LoyaltyCardSection';
import { usePointsScreenData } from '../../hooks/composite/usePointsScreenData';

export default function Points() {
  const { loyaltyQr, refresh } = usePointsScreenData();

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={false} onRefresh={refresh} />
        }
      >
        <HeaderSection />
        <Box className='px-6 pb-8'>
          <VStack space='xl'>
            <LoyaltyCardSection loyaltyQr={loyaltyQr} />
            <HowToEarnPointsSection />
          </VStack>
        </Box>
      </ScrollView>
    </Box>
  );
}
