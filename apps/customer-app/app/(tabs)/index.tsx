import { Box } from '@indie-points/ui-box';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import React from 'react';
import { ScrollView } from 'react-native';

import { HeaderSection } from '../../components/(tabs)/index/HeaderSection';
import { PointsErrorState } from '../../components/(tabs)/index/PointsErrorState';
import { PointsLoadingState } from '../../components/(tabs)/index/PointsLoadingState';
import { PointsSummaryCards } from '../../components/(tabs)/index/PointsSummaryCards';
import { useHomeScreenData } from '../../hooks/composite/useHomeScreenData';

export default function Home() {
  const { pointsData, loading, error, refreshing, refresh } =
    useHomeScreenData();

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={refresh} />
        }
      >
        <HeaderSection />
        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            <PointsLoadingState />
          ) : error ? (
            <PointsErrorState error={error} />
          ) : (
            <PointsSummaryCards pointsData={pointsData} />
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
