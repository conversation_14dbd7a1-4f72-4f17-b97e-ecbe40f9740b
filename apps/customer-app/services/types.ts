// Types for service layer responses

export interface CustomerPointsSummary {
  totalEarned: number;
  totalActive: number;
  totalRedeemed: number;
}

export interface CustomerTransaction {
  id: number;
  businessId: number;
  type: 'purchase' | 'redemption' | 'visit';
  businessName: string;
  businessCategory: string;
  pointsEarned: number;
  pointsRedeemed: number;
  amount: string;
  date: string;
}

export interface CustomerBusinessSummary {
  id: number;
  name: string;
  category: string;
  points: number;
  lastVisit: string;
}

export interface VisitTransactionResponse {
  alreadyExists: boolean;
}

export interface BusinessReward {
  id: string;
  businessId: string;
  title: string;
  description: string;
  pointsRequired: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerRewardEligibility {
  reward: BusinessReward;
  isEligible: boolean;
  customerPoints: number;
  pointsNeeded: number;
}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}
