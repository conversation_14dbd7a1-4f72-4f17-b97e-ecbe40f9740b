import { supabase } from '@indie-points/lib';

import { RewardService } from '../rewardService';

const select = jest.fn().mockReturnThis();
const eq = jest.fn().mockReturnThis();
const order = jest.fn().mockReturnThis();

jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
    from: jest.fn(() => ({ select, eq, order })),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('RewardService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessRewardsWithEligibility', () => {
    it('should return rewards with eligibility when successful', async () => {
      const rewardsData = [
        {
          id: 'reward-1',
          business_id: 'business-1',
          title: 'Free Coffee',
          description: 'Get a free coffee',
          points_required: 100,
          is_active: true,
          created_at: '2025-06-18T10:00:00Z',
          updated_at: '2025-06-18T10:00:00Z',
        },
      ];
      const pointsData = [
        {
          business_id: 'business-1',
          active_points: 120,
        },
      ];
      order.mockResolvedValue({
        data: rewardsData,
        error: null,
        count: 1,
        status: 200,
        statusText: 'OK',
      });
      mockSupabase.rpc.mockResolvedValue({
        data: pointsData,
        error: null,
        count: 1,
        status: 200,
        statusText: 'OK',
      });

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: [
          {
            reward: {
              id: 'reward-1',
              businessId: 'business-1',
              title: 'Free Coffee',
              description: 'Get a free coffee',
              pointsRequired: 100,
              isActive: true,
              createdAt: '2025-06-18T10:00:00Z',
              updatedAt: '2025-06-18T10:00:00Z',
            },
            isEligible: true,
            customerPoints: 120,
            pointsNeeded: 0,
          },
        ],
        error: null,
      });
    });

    it('should handle error fetching business rewards', async () => {
      order.mockResolvedValue({
        data: null,
        error: {
          message: 'Failed to fetch business rewards',
          details: '',
          hint: '',
          code: '',
          name: '',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: null,
        error: 'Failed to fetch business rewards',
      });
    });

    it('should handle no rewards data', async () => {
      order.mockResolvedValue({
        data: [],
        error: null,
        count: 0,
        status: 200,
        statusText: 'OK',
      });

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });

    it('should handle error fetching customer points', async () => {
      order.mockResolvedValue({
        data: [
          {
            id: 'reward-1',
            business_id: 'business-1',
            title: 'Free Coffee',
            description: 'Get a free coffee',
            points_required: 100,
            is_active: true,
            created_at: '2025-06-18T10:00:00Z',
            updated_at: '2025-06-18T10:00:00Z',
          },
        ],
        error: null,
        count: 1,
        status: 200,
        statusText: 'OK',
      });
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: {
          message: 'Failed to fetch customer points',
          details: '',
          hint: '',
          code: '',
          name: '',
        },
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: null,
        error: 'Failed to fetch customer points',
      });
    });

    it('should handle no points data for business', async () => {
      order.mockResolvedValue({
        data: [
          {
            id: 'reward-1',
            business_id: 'business-1',
            title: 'Free Coffee',
            description: 'Get a free coffee',
            points_required: 100,
            is_active: true,
            created_at: '2025-06-18T10:00:00Z',
            updated_at: '2025-06-18T10:00:00Z',
          },
        ],
        error: null,
        count: 1,
        status: 200,
        statusText: 'OK',
      });
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null,
        count: 0,
        status: 200,
        statusText: 'OK',
      });

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: [
          {
            reward: {
              id: 'reward-1',
              businessId: 'business-1',
              title: 'Free Coffee',
              description: 'Get a free coffee',
              pointsRequired: 100,
              isActive: true,
              createdAt: '2025-06-18T10:00:00Z',
              updatedAt: '2025-06-18T10:00:00Z',
            },
            isEligible: false,
            customerPoints: 0,
            pointsNeeded: 100,
          },
        ],
        error: null,
      });
    });

    it('should handle unexpected errors', async () => {
      order.mockRejectedValue(new Error('Network error'));

      const result = await RewardService.getBusinessRewardsWithEligibility(
        'customer-1',
        'business-1'
      );

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });
  });
});
