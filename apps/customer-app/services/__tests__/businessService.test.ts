import { supabase } from '@indie-points/lib';

import { BusinessService } from '../businessService';

jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('BusinessService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCustomerBusinessSummaries', () => {
    it('should return business data when successful', async () => {
      const mockData = [
        {
          business_id: 1,
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          active_points: 759,
          last_transaction_date: '11 Jun 2025',
        },
        {
          business_id: 2,
          business_name: 'Indie Points',
          business_type: 'System',
          active_points: 1,
          last_transaction_date: '11 Jun 2025',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 2,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_business_summaries',
        {
          p_customer_id: 'test-user-id',
        }
      );

      expect(result).toEqual({
        data: [
          {
            id: 1,
            name: 'StuMac Solutions',
            category: 'Gym/Fitness',
            points: 759,
            lastVisit: '11 Jun 2025',
          },
          {
            id: 2,
            name: 'Indie Points',
            category: 'System',
            points: 1,
            lastVisit: '11 Jun 2025',
          },
        ],
        error: null,
      });
    });

    it('should handle supabase errors', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should handle empty data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });

    it('should handle missing values in response data', async () => {
      const mockData = [
        {
          business_id: null,
          business_name: undefined,
          business_type: 'Test Category',
          active_points: null,
          last_transaction_date: '2025-06-17',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [
          {
            id: null,
            name: undefined,
            category: 'Test Category',
            points: null,
            lastVisit: '2025-06-17',
          },
        ],
        error: null,
      });
    });

    it('should handle null data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: null,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await BusinessService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });
  });
});
