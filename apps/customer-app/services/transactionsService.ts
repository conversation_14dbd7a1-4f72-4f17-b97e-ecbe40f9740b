import { supabase } from '@indie-points/lib';

import {
  CustomerTransaction,
  ServiceResponse,
  VisitTransactionResponse,
} from './types';

/**
 * Transactions service for handling customer transaction and business history operations
 */
export class TransactionsService {
  /**
   * Get customer transaction history including purchases and redemptions
   * @param customerId - The customer's UUID
   * @param page - Page number (default: 1)
   * @param pageSize - Number of transactions per page (default: 100)
   * @returns Promise with transaction history data or error
   */
  static async getCustomerTransactionHistory(
    customerId: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<ServiceResponse<CustomerTransaction[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_transaction_history',
        {
          p_customer_id: customerId,
          p_page: page,
          p_page_size: pageSize,
        }
      );

      if (error) {
        console.error('Error fetching customer transaction history:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch transaction history',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: CustomerTransaction[] = data.map((item: any) => ({
        id: item.transaction_id,
        businessId: item.business_id,
        type: item.transaction_type.toLowerCase(),
        businessName: item.business_name,
        businessCategory: item.business_type,
        pointsEarned: item.points_awarded,
        pointsRedeemed: item.points_redeemed,
        amount: item.amount_spent,
        date: item.created_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getCustomerTransactionHistory:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get customer transaction history for a specific business
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @param page - Page number (default: 1)
   * @param pageSize - Number of transactions per page (default: 100)
   * @returns Promise with transaction history data for the specific business or error
   */
  static async getCustomerTransactionHistoryForBusiness(
    customerId: string,
    businessId: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<ServiceResponse<CustomerTransaction[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_transaction_history_for_business',
        {
          p_customer_id: customerId,
          p_business_id: businessId,
          p_page: page,
          p_page_size: pageSize,
        }
      );

      if (error) {
        console.error(
          'Error fetching customer transaction history for business:',
          error
        );
        return {
          data: null,
          error:
            error.message || 'Failed to fetch transaction history for business',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: CustomerTransaction[] = data.map((item: any) => ({
        id: item.transaction_id,
        businessId: item.business_id,
        type: item.transaction_type.toLowerCase(),
        businessName: item.business_name,
        businessCategory: item.business_type,
        pointsEarned: item.points_awarded,
        pointsRedeemed: item.points_redeemed,
        amount: item.amount_spent,
        date: item.created_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getCustomerTransactionHistoryForBusiness:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Create a visit transaction for a customer at a business
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @param qrToken - The QR token from the scanned code
   * @param businessName - The business name from the scanned code
   * @returns Promise with visit transaction data or error
   */
  static async createVisitTransaction(
    customerId: string,
    businessId: string,
    businessName: string,
    qrToken: string
  ): Promise<ServiceResponse<VisitTransactionResponse>> {
    try {
      const { data, error } = await supabase.rpc('create_visit_transaction', {
        p_customer_id: customerId,
        p_business_id: businessId,
        p_business_name: businessName,
        p_qr_token: qrToken,
      });

      if (error) {
        console.error('Error creating visit transaction:', error);
        return {
          data: null,
          error: error.message || 'Failed to create visit transaction',
        };
      }

      // The function returns an array with a single object
      const transaction = data?.[0];

      if (!transaction) {
        return {
          data: null,
          error: 'No transaction data returned',
        };
      }

      return {
        data: {
          alreadyExists: transaction.already_exists,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in createVisitTransaction:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
