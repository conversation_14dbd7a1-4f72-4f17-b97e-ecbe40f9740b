import { supabase } from '@indie-points/lib';

import { CustomerBusinessSummary, ServiceResponse } from './types';

/**
 * Business service for handling business-related operations
 */
export class BusinessService {
  /**
   * Get customer business summaries including points earned per business
   * @param customerId - The customer's UUID
   * @returns Promise with business summaries data or error
   */
  static async getCustomerBusinessSummaries(
    customerId: string
  ): Promise<ServiceResponse<CustomerBusinessSummary[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_business_summaries',
        {
          p_customer_id: customerId,
        }
      );

      if (error) {
        console.error('Error fetching customer business summaries:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business summaries',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const businesses: CustomerBusinessSummary[] = data.map((item: any) => ({
        id: item.business_id,
        name: item.business_name,
        category: item.business_type,
        points: item.active_points,
        lastVisit: item.last_transaction_date,
      }));

      return {
        data: businesses,
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getCustomerBusinessSummaries:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
