import { supabase } from '@indie-points/lib';

import { CustomerRewardEligibility, ServiceResponse } from './types';

/**
 * Reward service for handling business rewards and eligibility
 */
export class RewardService {
  /**
   * Get available rewards for a business with customer eligibility
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @returns Promise with rewards and eligibility data or error
   */
  static async getBusinessRewardsWithEligibility(
    customerId: string,
    businessId: string
  ): Promise<ServiceResponse<CustomerRewardEligibility[]>> {
    try {
      // First get the business rewards
      const { data: rewardsData, error: rewardsError } = await supabase
        .from('business_rewards')
        .select('*')
        .eq('business_id', businessId)
        .eq('is_active', true)
        .order('points_required', { ascending: true });

      if (rewardsError) {
        console.error('Error fetching business rewards:', rewardsError);
        return {
          data: null,
          error: rewardsError.message || 'Failed to fetch business rewards',
        };
      }

      if (!rewardsData || rewardsData.length === 0) {
        return {
          data: [],
          error: null,
        };
      }

      // Get customer's current points for this business
      const { data: pointsData, error: pointsError } = await supabase.rpc(
        'get_customer_business_summaries',
        {
          p_customer_id: customerId,
        }
      );

      if (pointsError) {
        console.error('Error fetching customer points:', pointsError);
        return {
          data: null,
          error: pointsError.message || 'Failed to fetch customer points',
        };
      }

      // Find the customer's points for this specific business
      const businessSummary = pointsData?.find(
        (summary: any) => summary.business_id === businessId
      );
      const customerPoints = businessSummary?.active_points || 0;

      // Map rewards with eligibility information
      const rewardsWithEligibility: CustomerRewardEligibility[] =
        rewardsData.map((reward: any) => {
          const isEligible = customerPoints >= reward.points_required;
          const pointsNeeded = Math.max(
            0,
            reward.points_required - customerPoints
          );

          return {
            reward: {
              id: reward.id,
              businessId: reward.business_id,
              title: reward.title,
              description: reward.description,
              pointsRequired: reward.points_required,
              isActive: reward.is_active,
              createdAt: reward.created_at,
              updatedAt: reward.updated_at,
            },
            isEligible,
            customerPoints,
            pointsNeeded,
          };
        });

      return {
        data: rewardsWithEligibility,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getBusinessRewardsWithEligibility:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
