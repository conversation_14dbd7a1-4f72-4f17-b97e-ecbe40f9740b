import * as Haptics from 'expo-haptics';
import { useCallback, useState } from 'react';

export interface LoyaltyQrCodeOptions {
  expiryMs?: number; // How long the QR code is valid for (ms)
}

export function useLoyaltyQrCode(
  userId?: string,
  options: LoyaltyQrCodeOptions = {}
) {
  const expiryMs = options.expiryMs ?? 60 * 60 * 1000; // default 1 hour

  // Helper to generate QR code payload
  const generateQrPayload = useCallback(() => {
    const now = Date.now();
    return JSON.stringify({
      expiry: now + expiryMs,
      issuedAt: now,
      userId: userId ?? null,
    });
  }, [userId, expiryMs]);

  const [qrValue, setQrValue] = useState(generateQrPayload);
  const [qrWidth, setQrWidth] = useState(0);

  const regenerate = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setQrValue(generateQrPayload());
  }, [generateQrPayload]);

  return {
    qrValue,
    setQrWidth,
    qrWidth,
    regenerate,
  };
}
