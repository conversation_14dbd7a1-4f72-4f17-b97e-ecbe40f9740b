import { useCallback, useEffect, useState } from 'react';

import { PointsService } from '../../services/pointsService';
import { CustomerPointsSummary } from '../../services/types';

export function useCustomerPointsSummary(userId?: string) {
  const [data, setData] = useState<CustomerPointsSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId) {
      setData(null);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await PointsService.getCustomerPointsSummary(userId);
      setData(result.data);
      setError(result.error);
    } catch (err: any) {
      setData(null);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
