import { useCallback, useState } from 'react';

import { TransactionsService } from '../../services/transactionsService';
import { VisitTransactionResponse } from '../../services/types';

interface QRCodeData {
  businessId: string;
  businessName: string;
  qrToken: string;
}

export function useVisitTransaction(userId?: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [result, setResult] = useState<VisitTransactionResponse | null>(null);

  const submitVisit = useCallback(
    async (qrData: QRCodeData) => {
      if (!userId || !qrData) return;
      setLoading(true);
      setError(null);
      setSuccess(null);
      setResult(null);
      try {
        const response = await TransactionsService.createVisitTransaction(
          userId,
          qrData.businessId,
          qrData.businessName,
          qrData.qrToken
        );
        setResult(response.data);
        if (response.error) {
          setError(response.error);
        } else if (response.data) {
          if (response.data.alreadyExists) {
            setSuccess(`You have already visited ${qrData.businessName}!`);
          } else {
            setSuccess(
              `Success! You earned 1 point at ${qrData.businessName}!`
            );
          }
        }
      } catch (err: any) {
        setError(err?.message || 'Failed to process visit. Please try again.');
      } finally {
        setLoading(false);
      }
    },
    [userId]
  );

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setSuccess(null);
    setResult(null);
  }, []);

  return { loading, error, success, result, submitVisit, reset };
}
