import { useCallback, useEffect, useState } from 'react';

import { BusinessService } from '../../services/businessService';
import { CustomerBusinessSummary } from '../../services/types';

export function useCustomerBusinessSummaries(userId?: string) {
  const [data, setData] = useState<CustomerBusinessSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId) {
      setData([]);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await BusinessService.getCustomerBusinessSummaries(userId);
      setData(result.data || []);
      setError(result.error);
    } catch (err: any) {
      setData([]);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
