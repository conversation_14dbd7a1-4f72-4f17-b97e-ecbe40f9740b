import { useCallback, useEffect, useState } from 'react';

import { TransactionsService } from '../../services/transactionsService';
import { CustomerTransaction } from '../../services/types';

export function useCustomerTransactionHistory(
  userId?: string,
  page: number = 1,
  pageSize: number = 10
) {
  const [data, setData] = useState<CustomerTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);

  const fetchData = useCallback(async () => {
    if (!userId) {
      setData([]);
      setError(null);
      setLoading(false);
      setHasMore(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await TransactionsService.getCustomerTransactionHistory(
        userId,
        page,
        pageSize
      );
      setData(prevData =>
        page === 1 ? result.data || [] : [...prevData, ...(result.data || [])]
      );
      setError(result.error);
      setHasMore((result.data?.length || 0) === pageSize);
    } catch (err: any) {
      setData([]);
      setError(err?.message || 'An unexpected error occurred');
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [userId, page, pageSize]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, hasMore, refetch: fetchData };
}
