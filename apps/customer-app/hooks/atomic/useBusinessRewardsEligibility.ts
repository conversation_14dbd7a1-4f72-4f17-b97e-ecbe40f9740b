import { useCallback, useEffect, useState } from 'react';

import { RewardService } from '../../services/rewardService';
import { CustomerRewardEligibility } from '../../services/types';

export function useBusinessRewardsEligibility(
  userId?: string,
  businessId?: string
) {
  const [data, setData] = useState<CustomerRewardEligibility[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!userId || !businessId) {
      setData([]);
      setError(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const result = await RewardService.getBusinessRewardsWithEligibility(
        userId,
        businessId
      );
      setData(result.data || []);
      setError(result.error);
    } catch (err: any) {
      setData([]);
      setError(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [userId, businessId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
