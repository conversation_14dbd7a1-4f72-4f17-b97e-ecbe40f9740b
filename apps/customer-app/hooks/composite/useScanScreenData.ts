import { useAuth } from '@indie-points/contexts';
import { useCameraPermissions } from 'expo-camera';
import { useCallback, useEffect, useState } from 'react';

import { useVisitTransaction } from '../atomic/useVisitTransaction';

// Types for QR code data
interface QRCodeData {
  businessId: string;
  businessName: string;
  qrToken: string;
}

export function useScanScreenData() {
  const { user } = useAuth();
  const [facing] = useState<'back'>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const visitTransaction = useVisitTransaction(user?.id);
  const [scannedData, setScannedData] = useState<QRCodeData | null>(null);
  const [scanCompleted, setScanCompleted] = useState(false);

  useEffect(() => {
    if (permission && !permission.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  // Parse QR code data (UI-specific)
  const parseQRCodeData = (data: string): QRCodeData | null => {
    try {
      const parsed = JSON.parse(data);
      if (parsed.businessId && parsed.businessName && parsed.token) {
        return {
          businessId: parsed.businessId,
          businessName: parsed.businessName,
          qrToken: parsed.token,
        };
      }
      return null;
    } catch (error) {
      console.error('Error parsing QR code data:', error);
      return null;
    }
  };

  // Handle QR code scan
  const handleBarCodeScanned = useCallback(
    async (result: { data: string }) => {
      if (visitTransaction.loading || scanCompleted) return;
      const qrData = parseQRCodeData(result.data);
      if (!qrData) {
        setScanCompleted(true);
        setScannedData(null);
        // Show error via error state in hook
        await visitTransaction.submitVisit({
          businessId: '',
          businessName: '',
          qrToken: '',
        });
        return;
      }
      setScannedData(qrData);
      await visitTransaction.submitVisit(qrData);
      setScanCompleted(true);
    },
    [visitTransaction, scanCompleted]
  );

  // Unified reset for the screen
  const reset = useCallback(() => {
    setScanCompleted(false);
    setScannedData(null);
    visitTransaction.reset();
  }, [visitTransaction]);

  return {
    facing,
    permission,
    requestPermission,
    ...visitTransaction,
    scannedData,
    scanCompleted,
    handleBarCodeScanned,
    reset,
  };
}
