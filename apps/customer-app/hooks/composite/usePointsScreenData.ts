import { useAuth } from '@indie-points/contexts';

import { useLoyaltyQrCode } from '../atomic/useLoyaltyQrCode';

export function usePointsScreenData() {
  const { user } = useAuth();
  // Only atomic hook needed is for the QR code
  const loyaltyQr = useLoyaltyQrCode(user?.id);

  // Refresh just regenerates the QR code
  const refresh = async () => {
    loyaltyQr.regenerate();
    // Optionally add a small delay for UI feedback
    await new Promise(resolve => setTimeout(resolve, 500));
  };

  return {
    refresh,
    loyaltyQr,
  };
}
