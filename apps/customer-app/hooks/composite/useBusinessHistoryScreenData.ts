import { useAuth } from '@indie-points/contexts';
import { useCallback, useState } from 'react';

import { useBusinessRewardsEligibility } from '../atomic/useBusinessRewardsEligibility';
import { useBusinessTransactionHistory } from '../atomic/useBusinessTransactionHistory';

export function useBusinessHistoryScreenData(businessId?: string) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'transactions' | 'rewards'>(
    'rewards'
  );
  const [transactionPage, setTransactionPage] = useState(1);
  const PAGE_SIZE = 10;

  // Atomic hooks
  const transactions = useBusinessTransactionHistory(
    user?.id,
    businessId,
    transactionPage,
    PAGE_SIZE
  );
  const rewards = useBusinessRewardsEligibility(user?.id, businessId);

  // Unified loading and error
  const loading =
    activeTab === 'transactions' ? transactions.loading : rewards.loading;
  const error =
    activeTab === 'transactions' ? transactions.error : rewards.error;

  // Refresh handler
  const [refreshing, setRefreshing] = useState(false);
  const refresh = useCallback(async () => {
    setRefreshing(true);
    if (activeTab === 'transactions') {
      setTransactionPage(1);
      await transactions.refetch();
    } else {
      await rewards.refetch();
    }
    await new Promise(resolve => setTimeout(resolve, 750));
    setRefreshing(false);
  }, [activeTab, transactions, rewards]);

  // Load more for transactions
  const loadMoreTransactions = useCallback(async () => {
    if (
      transactions.loading ||
      !transactions.hasMore ||
      !user?.id ||
      !businessId
    )
      return;
    setTransactionPage(prev => prev + 1);
  }, [transactions.loading, transactions.hasMore, user?.id, businessId]);

  // Reset transaction page when switching to transactions tab
  const handleTabChange = (tab: 'transactions' | 'rewards') => {
    setActiveTab(tab);
    if (tab === 'transactions') {
      setTransactionPage(1);
    }
  };

  return {
    activeTab,
    setActiveTab: handleTabChange,
    transactionData: transactions.data,
    rewardsData: rewards.data,
    loading,
    error,
    refreshing,
    refresh,
    loadMoreTransactions,
    hasMoreTransactions: transactions.hasMore,
    loadingMoreTransactions: transactions.loading && transactionPage > 1,
    loadingRewards: rewards.loading,
    rewardsError: rewards.error,
  };
}
