import { useAuth } from '@indie-points/contexts';
import { useCallback, useState } from 'react';

import { useCustomerBusinessSummaries } from '../atomic/useCustomerBusinessSummaries';
import { useCustomerTransactionHistory } from '../atomic/useCustomerTransactionHistory';

export function useHistoryScreenData() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'transactions' | 'businesses'>(
    'businesses'
  );
  const [transactionPage, setTransactionPage] = useState(1);
  const PAGE_SIZE = 10;

  // Atomic hooks
  const transactions = useCustomerTransactionHistory(
    user?.id,
    transactionPage,
    PAGE_SIZE
  );
  const businesses = useCustomerBusinessSummaries(user?.id);

  // Unified loading and error
  const loading =
    activeTab === 'transactions' ? transactions.loading : businesses.loading;
  const error =
    activeTab === 'transactions' ? transactions.error : businesses.error;

  // Refresh handler
  const [refreshing, setRefreshing] = useState(false);
  const refresh = useCallback(async () => {
    setRefreshing(true);
    if (activeTab === 'transactions') {
      setTransactionPage(1);
      await transactions.refetch();
    } else {
      await businesses.refetch();
    }
    await new Promise(resolve => setTimeout(resolve, 750));
    setRefreshing(false);
  }, [activeTab, transactions, businesses]);

  // Load more for transactions
  const loadMoreTransactions = useCallback(async () => {
    if (transactions.loading || !transactions.hasMore || !user?.id) return;
    setTransactionPage(prev => prev + 1);
  }, [transactions.loading, transactions.hasMore, user?.id]);

  // Reset transaction page when switching to transactions tab
  const handleTabChange = (tab: 'transactions' | 'businesses') => {
    setActiveTab(tab);
    if (tab === 'transactions') {
      setTransactionPage(1);
    }
  };

  return {
    activeTab,
    setActiveTab: handleTabChange,
    transactionData: transactions.data,
    businessData: businesses.data,
    loading,
    error,
    refreshing,
    refresh,
    loadMoreTransactions,
    hasMoreTransactions: transactions.hasMore,
    loadingMoreTransactions: transactions.loading && transactionPage > 1,
  };
}
