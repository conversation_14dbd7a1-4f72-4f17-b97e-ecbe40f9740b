import { useAuth } from '@indie-points/contexts';

import { useCustomerPointsSummary } from '../atomic/useCustomerPointsSummary';

export function useHomeScreenData() {
  const { user } = useAuth();
  // Atomic hook for points summary
  const pointsSummary = useCustomerPointsSummary(user?.id);

  // Unified refresh for the screen
  const refresh = async () => {
    await Promise.all([
      pointsSummary.refetch(),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
  };

  return {
    pointsData: pointsSummary.data,
    loading: pointsSummary.loading,
    error: pointsSummary.error,
    refreshing: pointsSummary.loading, // Could be improved if more data sources are added
    refresh,
  };
}
