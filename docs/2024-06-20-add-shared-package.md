# How to Add a New Shared Package to the Monorepo

_Date: 2024-06-20_

This guide explains how to add a new shared package to the Indie Points monorepo. Follow these steps
to create a new package for shared code, or to refactor existing code into a shared package. This is
written for future reference to keep the process fast and consistent.

---

## 1. Create the Package Directory

From the root of the repo:

```sh
mkdir -p packages/<package-name>/src
```

Example:

```sh
mkdir -p packages/utils/src
```

---

## 2. Add Source Files

- Place your shared code in `packages/<package-name>/src/`.
- Example: `packages/utils/src/dateHelpers.ts`

---

## 3. Create `index.ts`

- In `packages/<package-name>/`, create an `index.ts` that exports from your source files:

```ts
export * from './src/dateHelpers';
```

---

## 4. Add a `package.json`

Create `packages/<package-name>/package.json`:

```json
{
  "name": "@indie-points/<package-name>",
  "version": "1.0.0",
  "private": true,
  "main": "./index.ts"
}
```

---

## 5. Update Root `package.json`

- Ensure `"packages/*"` is included in the `workspaces` array in the root `package.json`.
- Usually, this is already set up. If not, add it:

```json
"workspaces": [
  "apps/*",
  "packages/*"
]
```

---

## 6. Add TypeScript Path Alias (Optional but Recommended)

- In each app that will use the package, update `tsconfig.json`:

```json
"paths": {
  "@indie-points/<package-name>": ["../../packages/<package-name>"]
}
```

---

## 7. Update Imports

- Refactor code to import from the new package:

```ts
import { someHelper } from '@indie-points/utils';
```

---

## 8. Install/Link the Package

- Run `npm install` from the root to update workspace links:

```sh
npm install
```

---

## 9. Test Everything

- Run the linter and tests to ensure everything works:

```sh
npm run lint
npm test
```

---

## 10. (Optional) Add Types and tsconfig to the Package

- For larger packages, add a `tsconfig.json` and a `types` field in `package.json`.

---

## Notes

- **Never** add shared packages directly to the npm registry dependencies in app `package.json`
  files. Let the monorepo workspace handle it.
- Keep shared code generic and free of app-specific logic.
- Use the same naming convention: `@indie-points/<package-name>`.
- Update documentation if you change the process!

---

_Happy coding!_
