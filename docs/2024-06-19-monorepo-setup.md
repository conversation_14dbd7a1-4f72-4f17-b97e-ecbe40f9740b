# Monorepo Setup & Migration Guide

This document describes the steps taken to migrate the Indie Points Customer App to a modern
monorepo structure using npm workspaces, Expo, and NativeWind/Tailwind, with support for future
multi-app expansion.

---

## 1. Monorepo Structure

- All app code is now located in `apps/customer-app/`.
- All dependencies are managed in the root `package.json` using npm workspaces.
- Shared code and documentation can be placed in the `packages/` directory.

```
indie-points-customer-app/
  apps/
    customer-app/
      ...
  packages/
    docs/
      monorepo-setup.md
  package.json
  ...
```

---

## 2. Moving the App

- Moved all app-related directories (e.g., `app/`, `components/`, `assets/`, etc.) into
  `apps/customer-app/`.
- Moved `app.json` and `eas.json` into `apps/customer-app/`.
- Updated asset paths in `app.json` to be relative to its new location.
- Moved `global.css` into `apps/customer-app/`.

---

## 3. Configuration

### npm Workspaces

- Enabled npm workspaces in the root `package.json`:
  ```json
  "workspaces": ["apps/*", "packages/*"]
  ```
- Ran `npm install` to set up workspace symlinks.

### TypeScript

- Created/updated `apps/customer-app/tsconfig.json` to use path aliases (e.g., `@/components`).

### ESLint

- Updated `eslint.config.js` to use `eslint-import-resolver-typescript` and point to both the app
  and root `tsconfig.json` for correct alias resolution.

### Tailwind/NativeWind

- Moved `tailwind.config.js` into `apps/customer-app/` as a re-export of the root config, or as a
  standalone config if needed.
- Updated all references to `global.css` and Tailwind config in Metro and NativeWind setups.

---

## 4. Metro Bundler

- Removed the root `metro.config.js`.
- Added a dedicated `metro.config.js` in `apps/customer-app/`:
  ```js
  const { getDefaultConfig } = require('expo/metro-config');
  const { withNativeWind } = require('nativewind/metro');
  const config = getDefaultConfig(__dirname);
  module.exports = withNativeWind(config, {
    input: './global.css',
    tailwindConfig: './tailwind.config.js',
  });
  ```
- This allows each app to have its own Metro config, supporting multiple apps in the future.

---

## 5. Scripts

- Updated root `package.json` scripts for customer app:
  - `customer:start`: Start the app from the root.
  - `customer:build:dev`, `customer:build:preview`, `customer:build:prod`: Build scripts for EAS.
  - `format:check`, `format:write`, `lint`, `lint:fix`: Repo-wide scripts.
- All scripts are prefixed and sorted for clarity and future expansion.

---

## 6. Linting & Formatting

- Ensured ESLint and Prettier work repo-wide and with path aliases.
- Installed `eslint-import-resolver-typescript` for ESLint path alias support.

---

## 7. Multi-App Ready

- The structure and config support adding more apps in `apps/` and packages in `packages/`.
- Each app can have its own Metro config, Tailwind config, and scripts.

---

## 8. How to Start/Build the Customer App

From the root directory:

- Start: `npm run customer:start`
- Build (dev): `npm run customer:build:dev`
- Build (preview): `npm run customer:build:preview`
- Build (prod): `npm run customer:build:prod`

---

## 9. Troubleshooting

- If you add new path aliases, update both `tsconfig.json` and ESLint config.
- If you add a new app, copy the Metro config and adjust paths as needed.
- Always restart Metro with `npx expo start -c` after config changes.

---

For further details or to expand this documentation, add more markdown files to `packages/docs/`.
