module.exports = {
  preset: 'ts-jest/presets/js-with-ts',
  rootDir: '.',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testMatch: [
    '<rootDir>/apps/**/__tests__/**/*.[jt]s?(x)',
    '<rootDir>/apps/**/*.(spec|test).[jt]s?(x)',
    '<rootDir>/packages/**/__tests__/**/*.[jt]s?(x)',
    '<rootDir>/packages/**/*.(spec|test).[jt]s?(x)',
  ],
  setupFilesAfterEnv: [],
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
    },
  },
};
