import { <PERSON> } from 'expo-router';
import * as Web<PERSON>rowser from 'expo-web-browser';
import React from 'react';
import { Platform } from 'react-native';

export function ExternalLink({
  href,
  ...props
}: React.ComponentProps<typeof Link>) {
  return (
    <Link
      target='_blank'
      {...props}
      href={href}
      onPress={e => {
        if (Platform.OS !== 'web') {
          // Prevent the default behavior of linking to the default browser on native.
          e.preventDefault();
          // Open the link in an in-app browser.
          WebBrowser.openBrowserAsync(href as string);
        }
      }}
    />
  );
}
