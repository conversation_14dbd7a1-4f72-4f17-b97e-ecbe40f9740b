import { useAuth } from '@indie-points/contexts';
import { useState } from 'react';

interface FormState {
  email: string;
  password: string;
  confirmPassword?: string;
}

interface FormErrors {
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

export function useAuthForm(type: 'signin' | 'signup' | 'forgot' | 'reset') {
  const { signIn, signUp, resetPassword, updatePassword } = useAuth();
  const [formState, setFormState] = useState<FormState>({
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string): boolean => {
    return password.length >= 6;
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formState.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formState.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (type !== 'forgot') {
      if (!formState.password) {
        newErrors.password = 'Password is required';
      } else if (!validatePassword(formState.password)) {
        newErrors.password = 'Password must be at least 6 characters';
      }
    }

    if (type === 'signup' || type === 'reset') {
      if (!formState.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formState.password !== formState.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setErrors({});

    try {
      let result;

      switch (type) {
        case 'signin':
          result = await signIn(formState.email, formState.password);
          break;
        case 'signup':
          result = await signUp(formState.email, formState.password);
          break;
        case 'forgot':
          result = await resetPassword(formState.email);
          break;
        case 'reset':
          result = await updatePassword(formState.password);
          break;
      }

      if (result.error) {
        setErrors({ general: result.error.message });
      }
    } catch {
      setErrors({ general: 'An unexpected error occurred' });
    } finally {
      setLoading(false);
    }
  };

  const updateField = (field: keyof FormState, value: string) => {
    setFormState(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return {
    formState,
    errors,
    loading,
    updateField,
    handleSubmit,
  };
}
