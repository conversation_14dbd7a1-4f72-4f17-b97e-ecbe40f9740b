import { supabase } from '@indie-points/lib';
import { act, renderHook } from '@testing-library/react-native';
import React from 'react';

import { AuthProvider, useAuth } from '../AuthContext';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signInWithOAuth: jest.fn(),
      signOut: jest.fn(),
      resetPasswordForEmail: jest.fn(),
      updateUser: jest.fn(),
    },
    rpc: jest.fn(),
  },
}));

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    replace: jest.fn(),
  }),
}));

// Mock expo-auth-session
jest.mock('expo-auth-session', () => ({
  makeRedirectUri: jest.fn(() => 'test://redirect'),
}));

// Mock expo-web-browser
jest.mock('expo-web-browser', () => ({
  openAuthSessionAsync: jest.fn(),
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('AuthContext', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <AuthProvider>{children}</AuthProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock initial session
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    });

    // Mock auth state change listener
    mockSupabase.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  describe('deleteAccount', () => {
    it('should delete account when user is authenticated', async () => {
      // Mock successful RPC call
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null,
        count: null,
        status: 200,
        statusText: 'OK',
      });

      // Mock router replace
      const mockReplace = jest.fn();
      jest.doMock('expo-router', () => ({
        useRouter: () => ({
          replace: mockReplace,
        }),
      }));

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Set a mock user
      act(() => {
        (result.current as any).user = { id: 'test-user-id' };
      });

      let deleteResult;
      await act(async () => {
        deleteResult = await result.current.deleteAccount();
      });

      expect(deleteResult).toEqual({ error: null });
      expect(mockSupabase.rpc).toHaveBeenCalledWith('delete_user_account');
      // Note: We no longer call signOut since the user has been deleted from the database
    });

    it('should return error when no user is logged in', async () => {
      const { result } = renderHook(() => useAuth(), { wrapper });

      let deleteResult;
      await act(async () => {
        deleteResult = await result.current.deleteAccount();
      });

      expect(deleteResult).toEqual({
        error: {
          message: 'No user logged in',
          name: 'NoUserError',
        },
      });
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
    });

    it('should return error when RPC call fails', async () => {
      const mockError = {
        message: 'Database error',
        code: '500',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: mockError,
        count: null,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Set a mock user
      act(() => {
        (result.current as any).user = { id: 'test-user-id' };
      });

      let deleteResult;
      await act(async () => {
        deleteResult = await result.current.deleteAccount();
      });

      expect(deleteResult).toEqual({
        error: {
          message: 'Database error',
          name: 'DeleteAccountError',
        },
      });
      expect(mockSupabase.rpc).toHaveBeenCalledWith('delete_user_account');
      // Note: We don't call signOut when RPC fails, so the user remains logged in
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Set a mock user
      act(() => {
        (result.current as any).user = { id: 'test-user-id' };
      });

      let deleteResult;
      await act(async () => {
        deleteResult = await result.current.deleteAccount();
      });

      expect(deleteResult).toEqual({
        error: {
          message: 'Network error',
          name: 'Error',
        },
      });
    });

    it('should handle non-Error exceptions', async () => {
      mockSupabase.rpc.mockRejectedValue('String error');

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Set a mock user
      act(() => {
        (result.current as any).user = { id: 'test-user-id' };
      });

      let deleteResult;
      await act(async () => {
        deleteResult = await result.current.deleteAccount();
      });

      expect(deleteResult).toEqual({
        error: {
          message: 'An unexpected error occurred',
          name: 'UnknownError',
        },
      });
    });
  });
});
