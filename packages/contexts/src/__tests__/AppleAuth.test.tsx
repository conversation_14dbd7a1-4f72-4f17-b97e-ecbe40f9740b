import { supabase } from '@indie-points/lib';
import { act, renderHook } from '@testing-library/react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import React from 'react';

import { AuthProvider, useAuth } from '../AuthContext';

// Mock the dependencies
jest.mock('@indie-points/lib', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signInWithIdToken: jest.fn(),
      signInWithOAuth: jest.fn(),
    },
  },
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    replace: jest.fn(),
  }),
}));

jest.mock('expo-apple-authentication', () => ({
  signInAsync: jest.fn(),
  AppleAuthenticationScope: {
    FULL_NAME: 'fullName',
    EMAIL: 'email',
  },
}));

jest.mock('expo-auth-session', () => ({
  makeRedirectUri: jest.fn(() => 'test://redirect'),
}));

jest.mock('expo-web-browser', () => ({
  openAuthSessionAsync: jest.fn(),
}));

jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

const mockSupabase = supabase as any;
const mockAppleAuth = AppleAuthentication as any;

describe('Apple Sign-In', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (mockSupabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
      error: null,
    });

    (mockSupabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should handle Apple Sign-In successfully on iOS', async () => {
    const mockCredential = {
      identityToken: 'mock-identity-token',
      user: 'mock-user-id',
      state: null,
      fullName: null,
      email: null,
      realUserStatus: 1,
      authorizationCode: null,
    };

    const mockSession = {
      access_token: 'mock-access-token',
      user: { id: 'mock-user-id', email: '<EMAIL>' },
    };

    (mockAppleAuth.signInAsync as jest.Mock).mockResolvedValue(mockCredential);
    (mockSupabase.auth.signInWithIdToken as jest.Mock).mockResolvedValue({
      data: { session: mockSession, user: mockSession.user },
      error: null,
    });

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      const response = await result.current.signInWithProvider('apple');
      expect(response.error).toBeNull();
    });

    expect(mockAppleAuth.signInAsync).toHaveBeenCalledWith({
      requestedScopes: ['fullName', 'email'],
    });

    expect(mockSupabase.auth.signInWithIdToken).toHaveBeenCalledWith({
      provider: 'apple',
      token: 'mock-identity-token',
    });
  });

  it('should handle Apple Sign-In cancellation', async () => {
    const mockError = { code: 'ERR_REQUEST_CANCELED' };
    (mockAppleAuth.signInAsync as jest.Mock).mockRejectedValue(mockError);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      const response = await result.current.signInWithProvider('apple');
      expect(response.error?.message).toBe('Apple Sign-In was cancelled');
    });
  });

  it('should handle missing identity token', async () => {
    const mockCredential = {
      identityToken: null,
      user: 'mock-user-id',
      state: null,
      fullName: null,
      email: null,
      realUserStatus: 1,
      authorizationCode: null,
    };

    (mockAppleAuth.signInAsync as jest.Mock).mockResolvedValue(mockCredential);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      const response = await result.current.signInWithProvider('apple');
      expect(response.error?.message).toBe(
        'No identity token received from Apple'
      );
    });
  });
});
