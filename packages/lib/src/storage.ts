// Platform detection
const isWeb =
  typeof window !== 'undefined' && typeof window.document !== 'undefined';

export const getStorage = () => {
  if (isWeb) {
    // Minimal localStorage adapter for Supabase
    return {
      getItem: (key: string) => Promise.resolve(localStorage.getItem(key)),
      setItem: (key: string, value: string) => {
        localStorage.setItem(key, value);
        return Promise.resolve();
      },
      removeItem: (key: string) => {
        localStorage.removeItem(key);
        return Promise.resolve();
      },
    };
  } else {
    // For native platforms, use AsyncStorage
    // Dynamically import AsyncStorage to avoid issues in web builds
    return {
      getItem: async (key: string) => {
        try {
          const AsyncStorage = await import(
            '@react-native-async-storage/async-storage'
          );
          return await AsyncStorage.default.getItem(key);
        } catch (error) {
          console.error('Error getting item from AsyncStorage:', error);
          return null;
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          const AsyncStorage = await import(
            '@react-native-async-storage/async-storage'
          );
          await AsyncStorage.default.setItem(key, value);
        } catch (error) {
          console.error('Error setting item in AsyncStorage:', error);
        }
      },
      removeItem: async (key: string) => {
        try {
          const AsyncStorage = await import(
            '@react-native-async-storage/async-storage'
          );
          await AsyncStorage.default.removeItem(key);
        } catch (error) {
          console.error('Error removing item from AsyncStorage:', error);
        }
      },
    };
  }
};
