# Contributing to Indie Points Customer App 🌟

Thank you for your interest in contributing to the Indie Points Customer App! This document provides
comprehensive guidelines for contributing to this React Native monorepo project, whether you're a
human developer or an AI tool.

## Table of Contents

- [Project Overview](#project-overview)
- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Component Development](#component-development)
- [Service Layer Guidelines](#service-layer-guidelines)
- [Authentication & Security](#authentication--security)
- [UI/UX Guidelines](#uiux-guidelines)
- [Performance Considerations](#performance-considerations)
- [Git Workflow](#git-workflow)
- [Pull Request Process](#pull-request-process)
- [Code Review Guidelines](#code-review-guidelines)
- [Troubleshooting](#troubleshooting)
- [Resources](#resources)

## Project Overview

The Indie Points Customer App is a React Native loyalty application built with:

- **Framework**: Expo (React Native)
- **UI Library**: Gluestack UI with Tailwind CSS (NativeWind)
- **Authentication**: Supabase Auth
- **Navigation**: Expo Router (file-based routing)
- **Language**: TypeScript
- **Architecture**: Monorepo with shared packages
- **Testing**: Jest with React Native Testing Library
- **Linting**: ESLint with Expo configuration
- **Formatting**: Prettier

The project consists of two main applications:

- **Customer App** (`apps/customer-app/`): End-user loyalty app
- **Business App** (`apps/business-app/`): Business management app

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development)
- Android Studio (for Android development)
- Git

### Initial Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd indie-points-customer-app
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables** Create a `.env` file in the root directory:

   ```bash
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start development**

   ```bash
   # For customer app
   npm run customer:start

   # For business app
   npm run business:start
   ```

## Project Structure

```
indie-points-customer-app/
├── apps/
│   ├── customer-app/          # Customer-facing loyalty app
│   │   ├── app/              # Expo Router pages
│   │   ├── services/         # API services and types
│   │   └── assets/           # Images, fonts, etc.
│   └── business-app/         # Business management app
│       ├── app/              # Expo Router pages
│       ├── services/         # API services and types
│       └── assets/           # Images, fonts, etc.
├── packages/                 # Shared packages
│   ├── auth/                # Authentication components
│   ├── components/          # Shared UI components
│   ├── contexts/            # React contexts
│   ├── forms/               # Form components
│   ├── hooks/               # Custom hooks
│   ├── lib/                 # Utilities and configurations
│   └── ui-*/                # Gluestack UI components
├── docs/                    # Documentation
└── root config files        # ESLint, Jest, TypeScript, etc.
```

## Development Setup

### Available Scripts

```bash
# Development
npm run start:business      # Start business app (port 8082)
npm run start:business:web  # Start business app on web (port 8082)
npm run start:customer      # Start customer app (port 8081)
npm run start:customer:web  # Start customer app on web (port 8081)

# Building
npm run build:business:dev      # Build business app (development)
npm run build:business:preview  # Build business app (preview)
npm run build:business:prod     # Build business app (production)
npm run build:customer:dev      # Build customer app (development)
npm run build:customer:preview  # Build customer app (preview)
npm run build:customer:prod     # Build customer app (production)

# Code Quality
npm run check              # Run all checks (format, lint, test)
npm run format:check       # Check code formatting
npm run format:write       # Format code
npm run lint               # Run ESLint
npm run lint:fix           # Fix ESLint issues
npm run test               # Run tests
npm run test:watch         # Run tests in watch mode
```

### Port Configuration

The apps are configured to run on different ports to avoid conflicts:

- **Customer App**: Port 8081 (default Expo port)
- **Business App**: Port 8082

To run both apps simultaneously, use separate terminal windows.

### Development Workflow

1. **Start the development server**

   ```bash
   # For customer app
   npm run start:customer

   # For business app (in a separate terminal)
   npm run start:business
   ```

2. **Open in simulator/emulator**

   - iOS: Press `i` in the terminal or use `npm run ios`
   - Android: Press `a` in the terminal or use `npm run android`
   - Web: Press `w` in the terminal or use `npm run web`

3. **Make changes** - The app will hot reload automatically

4. **Run quality checks before committing**
   ```bash
   npm run check
   ```

### Troubleshooting QR Code Visibility

If you can't see QR codes clearly when running both apps:

1. **Use `npm run start:separate`** - This provides better output formatting with clear prefixes
2. **Run apps individually** - Use separate terminal windows for each app
3. **Check terminal size** - Ensure your terminal window is large enough to display both outputs
4. **Use web interface** - Run `npm run start:web` to access apps via browser instead of QR codes

## Coding Standards

### TypeScript Guidelines

- **Strict Mode**: Always use strict TypeScript configuration
- **Type Definitions**: Define interfaces for all data structures
- **Service Responses**: Use consistent `ServiceResponse<T>` pattern:

```typescript
interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}
```

- **Error Handling**: Always handle errors gracefully with proper typing

### File Naming Conventions

- **Components**: PascalCase (e.g., `FormField.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Services**: camelCase with `Service` suffix (e.g., `pointsService.ts`)
- **Types**: camelCase (e.g., `types.ts`)
- **Tests**: Same name as file with `.test.ts` or `.spec.ts` suffix

### Import Organization

```typescript
// 1. React and React Native imports
import React from 'react';
import { View, Text } from 'react-native';

// 2. Third-party library imports
import { useAuth } from '@indie-points/contexts';

// 3. Local imports
import { FormField } from '@indie-points/forms';
import { Button } from '@indie-points/ui-button';

// 4. Type imports
import type { CustomerPointsSummary } from '../types';
```

### Component Structure

```typescript
import React from 'react';
import { View } from 'react-native';
import { Text } from '@indie-points/ui-text';

interface ComponentProps {
  title: string;
  onPress?: () => void;
}

export function Component({ title, onPress }: ComponentProps) {
  return (
    <View>
      <Text>{title}</Text>
    </View>
  );
}
```

## Testing Guidelines

### Test Structure

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test service layer and API interactions
- **Test Location**: Place tests in `__tests__` folders or alongside files

### Testing Patterns

```typescript
import { render, screen } from '@testing-library/react-native';
import { PointsService } from '../pointsService';

// Mock external dependencies
jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
  },
}));

describe('PointsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return points data when successful', async () => {
    // Arrange
    const mockData = [{ total_earned: 1500, total_active: 750 }];

    // Act
    const result = await PointsService.getCustomerPointsSummary('user-id');

    // Assert
    expect(result.data).toEqual({
      totalEarned: 1500,
      totalActive: 750,
    });
  });
});
```

### Test Coverage Requirements

- **Services**: 90%+ coverage
- **Components**: 80%+ coverage
- **Hooks**: 90%+ coverage
- **Utilities**: 95%+ coverage

## Component Development

### Gluestack UI Guidelines

- **Use Gluestack UI components** from the `@indie-points/ui-*` packages
- **Follow design system** colors and spacing
- **Responsive design** using Tailwind classes
- **Accessibility** with proper ARIA labels and semantic markup

### Component Examples

```typescript
import React from 'react';
import { VStack } from '@indie-points/ui-vstack';
import { Text } from '@indie-points/ui-text';
import { Button } from '@indie-points/ui-button';

interface PointsCardProps {
  points: number;
  onRedeem?: () => void;
}

export function PointsCard({ points, onRedeem }: PointsCardProps) {
  return (
    <VStack space="md" p="md" bg="background.100" rounded="lg">
      <Text fontSize="2xl" fontWeight="bold" textAlign="center">
        {points} Points
      </Text>
      {onRedeem && (
        <Button onPress={onRedeem} variant="solid">
          <ButtonText>Redeem Points</ButtonText>
        </Button>
      )}
    </VStack>
  );
}
```

### Styling Guidelines

- **Use Tailwind classes** for styling
- **Follow design tokens** from the theme configuration
- **Mobile-first approach** with responsive design
- **Dark mode support** using CSS variables

## Service Layer Guidelines

### Service Structure

```typescript
import { supabase } from '@indie-points/lib';
import { ServiceResponse, CustomerPointsSummary } from './types';

export class PointsService {
  static async getCustomerPointsSummary(
    customerId: string
  ): Promise<ServiceResponse<CustomerPointsSummary>> {
    try {
      const { data, error } = await supabase.rpc('get_customer_points_summary', {
        p_customer_id: customerId,
      });

      if (error) {
        console.error('Error fetching points summary:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch points summary',
        };
      }

      return {
        data: this.transformData(data),
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unexpected error',
      };
    }
  }

  private static transformData(data: any): CustomerPointsSummary {
    // Transform raw data to typed interface
    return {
      totalEarned: data.total_earned || 0,
      totalActive: data.total_active || 0,
      totalRedeemed: data.total_redeemed || 0,
    };
  }
}
```

### Error Handling

- **Consistent error format** across all services
- **Proper logging** for debugging
- **User-friendly error messages**
- **Graceful degradation** when services fail

## Authentication & Security

### Authentication Flow

- **Use AuthContext** for authentication state management
- **Secure token storage** with AsyncStorage
- **Automatic session refresh** handling
- **Protected routes** with proper redirects

### Security Best Practices

- **Never expose sensitive data** in client-side code
- **Validate all inputs** on both client and server
- **Use HTTPS** for all API communications
- **Implement proper CORS** policies
- **Sanitize user inputs** to prevent injection attacks

## UI/UX Guidelines

### Design System

- **Consistent spacing** using Tailwind spacing scale
- **Typography hierarchy** with proper font weights
- **Color consistency** using design tokens
- **Component variants** for different states

### Accessibility

- **Semantic HTML** structure
- **ARIA labels** for screen readers
- **Keyboard navigation** support
- **Color contrast** compliance
- **Touch target sizes** (minimum 44px)

### Responsive Design

- **Mobile-first approach**
- **Flexible layouts** using Flexbox
- **Breakpoint considerations** for different screen sizes
- **Touch-friendly interactions**

## Performance Considerations

### React Native Optimization

- **Use React.memo** for expensive components
- **Implement useMemo** and useCallback for expensive calculations
- **Optimize re-renders** with proper dependency arrays
- **Lazy load** components when possible

### Bundle Size

- **Tree shaking** for unused imports
- **Code splitting** for large components
- **Optimize images** and assets
- **Monitor bundle size** regularly

### Network Optimization

- **Implement caching** strategies
- **Optimize API calls** with proper pagination
- **Use background sync** for offline support
- **Compress data** when possible

## Git Workflow

### Branch Naming

- **Feature branches**: `feature/description`
- **Bug fixes**: `fix/description`
- **Hotfixes**: `hotfix/description`
- **Documentation**: `docs/description`

### Commit Messages

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

Examples:

```
feat(auth): add social login with Google
fix(points): resolve points calculation bug
docs(readme): update setup instructions
```

### Branch Strategy

1. **Main branch**: Production-ready code
2. **Develop branch**: Integration branch for features
3. **Feature branches**: Individual feature development
4. **Release branches**: Preparation for releases

## Pull Request Process

### Before Submitting

1. **Run all checks**

   ```bash
   npm run check
   ```

2. **Update documentation** if needed
3. **Add tests** for new functionality
4. **Update types** if interfaces changed

### PR Template

```markdown
## Description

Brief description of changes

## Type of Change

- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing

- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)

Add screenshots for UI changes

## Checklist

- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## Code Review Guidelines

### Review Checklist

- **Code quality**: Readable, maintainable, follows patterns
- **Functionality**: Works as expected, handles edge cases
- **Performance**: No obvious performance issues
- **Security**: No security vulnerabilities
- **Testing**: Adequate test coverage
- **Documentation**: Clear and up-to-date

### Review Process

1. **Automated checks** must pass
2. **At least one approval** required
3. **Address all comments** before merging
4. **Squash commits** when merging

## Troubleshooting

### Common Issues

**Metro bundler issues**

```bash
npm run customer:start -- --clear
```

**iOS build issues**

```bash
cd apps/customer-app
npx expo run:ios --clear
```

**Android build issues**

```bash
cd apps/customer-app
npx expo run:android --clear
```

**TypeScript errors**

```bash
npx tsc --noEmit
```

### Development Environment

- **Node.js version**: Use Node.js 18 or higher
- **Expo CLI**: Keep updated to latest version
- **Simulators**: Keep iOS/Android simulators updated
- **Dependencies**: Regular `npm install` to update packages

## Resources

### Documentation

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Gluestack UI Documentation](https://ui.gluestack.io/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [Supabase Documentation](https://supabase.com/docs)

### Community

- [Expo Discord](https://discord.gg/expo)
- [React Native Community](https://github.com/react-native-community)
- [Gluestack Community](https://discord.gg/gluestack)

### Tools

- [Expo Dev Tools](https://expo.dev/tools)
- [React Native Debugger](https://github.com/jhen0409/react-native-debugger)
- [Flipper](https://fbflipper.com/)

---

Thank you for contributing to the Indie Points Customer App! Your contributions help make this
project better for everyone. If you have any questions or need clarification on any of these
guidelines, please don't hesitate to ask.
