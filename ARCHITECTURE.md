# 🏗️ Mobile App Architecture Guide

This document describes a clean, layered architecture pattern for building scalable and maintainable
mobile applications.

## Overview

The application follows a layered architecture that promotes separation of concerns, testability,
and maintainability. The architecture consists of five main layers:

```
┌─────────────────────────────┐
│        UI Components        │ ← Pure presentation components
├─────────────────────────────┤
│         Screens             │ ← Screen-level components
├─────────────────────────────┤
│      Composite Hooks       │ ← Screen-specific data logic
├─────────────────────────────┤
│       Atomic Hooks          │ ← Single-purpose hooks
├─────────────────────────────┤
│      Service Layer          │ ← Business logic & data fetching
├─────────────────────────────┤
│     Backend/Database        │ ← Data persistence layer
└─────────────────────────────┘
```

## Layer Details

### 1. Service Layer

**Purpose**: Abstracts backend communication with business logic, caching, error handling, and retry
mechanisms.

**Key Features**:

- Service-level caching with automatic expiration
- Retry logic with exponential backoff
- Consistent error handling across the app
- Data transformation and validation
- Request deduplication to prevent redundant calls

**Responsibilities**:

- API communication
- Data transformation between external and internal formats
- Business rule enforcement
- Caching strategies
- Error handling and logging

**Example Structure**:

```typescript
export class BaseService {
  protected cache: Map<string, any> = new Map();
  protected retryConfig = { attempts: 3, backoff: 1000 };

  async makeRequest(endpoint: string, options?: RequestOptions) {
    // Caching, retry logic, error handling
  }
}

export class UserService extends BaseService {
  async getProfile(userId: string) {
    // Implementation with caching and error handling
  }

  async updateProfile(userId: string, data: ProfileData) {
    // Implementation with validation and persistence
  }
}
```

### 2. Atomic Hooks

**Purpose**: Single-responsibility hooks that wrap individual service methods with React state
management.

**Key Features**:

- One hook per service method or data operation
- Consistent loading, error, and data state management
- Automatic refetching capabilities
- Standardized return interface

**Responsibilities**:

- Convert service calls to React state
- Manage async operation states (loading, error, success)
- Provide data refetching mechanisms
- Handle component lifecycle integration

**Example Pattern**:

```typescript
export function useUserProfile(userId?: string) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    if (!userId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await userService.getProfile(userId);
      setData(result);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  return { data, loading, error, refetch: fetchData };
}
```

### 3. Composite Hooks

**Purpose**: Screen-level hooks that combine multiple atomic hooks to serve complete user
interfaces.

**Key Features**:

- Combines related atomic hooks for screen-level functionality
- Manages inter-hook dependencies and data relationships
- Provides unified refresh capabilities
- Orchestrates complex state interactions

**Responsibilities**:

- Coordinate multiple data sources
- Manage screen-level state and interactions
- Provide unified loading and error states
- Handle cross-cutting concerns for screens

**Example Pattern**:

```typescript
export function useProfileScreen(userId: string) {
  const profile = useUserProfile(userId);
  const preferences = useUserPreferences(userId);
  const activity = useUserActivity(userId, { limit: 10 });

  const refreshAll = useCallback(async () => {
    await Promise.all([profile.refetch(), preferences.refetch(), activity.refetch()]);
  }, [profile.refetch, preferences.refetch, activity.refetch]);

  const loading = profile.loading || preferences.loading || activity.loading;
  const hasError = profile.error || preferences.error || activity.error;

  return {
    profile: profile.data,
    preferences: preferences.data,
    activity: activity.data,
    loading,
    error: hasError,
    refresh: refreshAll,
  };
}
```

### 4. Screens

**Purpose**: Top-level screen components that orchestrate UI and manage navigation flow.

**Key Features**:

- Uses composite hooks for comprehensive data management
- Handles navigation, routing, and screen transitions
- Manages screen-level UI state (modals, refresh indicators)
- Minimal business logic - focuses on user experience

**Responsibilities**:

- Screen layout and structure
- Navigation handling
- User interaction coordination
- Screen-level state management (UI state only)

**Example Pattern**:

```typescript
export default function ProfileScreen({ userId }: Props) {
  const { profile, preferences, activity, loading, error, refresh } =
    useProfileScreen(userId);

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  if (loading && !profile) return <LoadingScreen />;
  if (error && !profile) return <ErrorScreen error={error} onRetry={refresh} />;

  return (
    <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}>
      <ProfileHeader data={profile} />
      <PreferencesSection data={preferences} />
      <ActivitySection data={activity} />
    </ScrollView>
  );
}
```

### 5. UI Components

**Purpose**: Pure, reusable presentation components with no data fetching or business logic.

**Key Features**:

- No data fetching, service calls, or business logic
- All data and callbacks passed via props
- Highly reusable across different screens and contexts
- Focused purely on presentation and user interaction

**Responsibilities**:

- Data presentation and formatting
- User input handling (via callbacks)
- Visual styling and layout
- Accessibility implementation

**Example Pattern**:

```typescript
interface ProfileHeaderProps {
  data: UserProfile | null;
  loading?: boolean;
  onEditPress?: () => void;
  onImagePress?: () => void;
}

export function ProfileHeader({
  data,
  loading = false,
  onEditPress,
  onImagePress
}: ProfileHeaderProps) {
  if (loading) return <ProfileHeaderSkeleton />;
  if (!data) return <EmptyProfileHeader />;

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onImagePress}>
        <Avatar source={{ uri: data.avatarUrl }} />
      </TouchableOpacity>
      <Text style={styles.name}>{data.name}</Text>
      <Button title="Edit" onPress={onEditPress} />
    </View>
  );
}
```

## Data Flow

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Screen    │────│ Composite Hook  │────│  Atomic Hook    │
└─────────────┘    └─────────────────┘    └─────────────────┘
        │                   │                       │
        │                   │                       │
        ▼                   ▼                       ▼
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│UI Component │    │  UI Component   │    │    Service      │
└─────────────┘    └─────────────────┘    └─────────────────┘
                                                   │
                                                   ▼
                                          ┌─────────────────┐
                                          │ Backend/Database│
                                          └─────────────────┘
```

**Flow Process**:

1. **Screen** calls **Composite Hook** for all screen data needs
2. **Composite Hook** orchestrates multiple **Atomic Hooks**
3. **Atomic Hooks** call appropriate **Services** for data operations
4. **Services** interact with **Backend/Database** systems
5. Data flows back up through the layers with proper state management
6. **Screen** passes processed data to **UI Components** via props

## Architecture Benefits

### 🎯 **Single Responsibility Principle**

Each layer has a clear, focused purpose:

- **Services**: Data operations and business logic
- **Atomic hooks**: Individual data state management
- **Composite hooks**: Screen-level data orchestration
- **Screens**: Navigation, layout, and user experience
- **UI Components**: Pure presentation and interaction

### 🧪 **Enhanced Testability**

Each layer can be tested independently:

- **Services**: Unit tested with mocked backend responses
- **Hooks**: Tested with mocked services and React testing utilities
- **Components**: Tested with mocked data and interaction callbacks
- **Screens**: Integration tested with mocked composite hooks

### ♻️ **Maximum Reusability**

- **UI components** work across any screen or context
- **Atomic hooks** can be reused in different composite combinations
- **Services** can be shared across multiple applications
- **Patterns** are consistent and predictable

### 🔧 **Improved Maintainability**

- Changes propagate predictably through well-defined layers
- Easy to modify individual layers without affecting others
- Clear boundaries prevent coupling between concerns
- Debugging is simplified with clear data flow

### ⚡ **Optimized Performance**

- Built-in caching at the service level
- Request deduplication prevents redundant network calls
- Efficient state management with React hooks
- Lazy loading and code splitting opportunities

## Implementation Guidelines

### Service Layer Best Practices

```typescript
// ✅ Good: Consistent error handling and caching
class ApiService extends BaseService {
  async fetchData(id: string, options: FetchOptions = {}) {
    const cacheKey = `data-${id}`;

    if (options.useCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await this.makeRequest(`/api/data/${id}`);
      const transformedData = this.transformResponse(response);

      if (options.useCache) {
        this.cache.set(cacheKey, transformedData);
      }

      return { data: transformedData, error: null };
    } catch (error) {
      this.logError('fetchData', error);
      return { data: null, error: error.message };
    }
  }
}
```

### Hook Layer Best Practices

```typescript
// ✅ Good: Consistent interface and proper cleanup
export function useDataFetcher(id?: string, options: HookOptions = {}) {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
  });

  const fetchData = useCallback(async () => {
    if (!id) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.fetchData(id, options);
      setState({
        data: result.data,
        loading: false,
        error: result.error,
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error.message,
      });
    }
  }, [id, options]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refetch: fetchData,
  };
}
```

### Component Layer Best Practices

```typescript
// ✅ Good: Pure presentation with proper TypeScript
interface DataDisplayProps {
  data: DataType | null;
  loading: boolean;
  error: string | null;
  onRefresh?: () => void;
  onItemPress?: (item: DataType) => void;
}

export const DataDisplay: React.FC<DataDisplayProps> = ({
  data,
  loading,
  error,
  onRefresh,
  onItemPress
}) => {
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} onRetry={onRefresh} />;
  if (!data) return <EmptyState />;

  return (
    <FlatList
      data={data.items}
      renderItem={({ item }) => (
        <DataItem
          item={item}
          onPress={() => onItemPress?.(item)}
        />
      )}
      refreshControl={
        onRefresh ? <RefreshControl refreshing={loading} onRefresh={onRefresh} /> : undefined
      }
    />
  );
};
```

## Package Organization

```
packages/
├── services/                    # Service layer
│   ├── src/
│   │   ├── base/               # Base service classes
│   │   ├── api/                # API service implementations
│   │   ├── cache/              # Caching mechanisms
│   │   ├── types/              # Service type definitions
│   │   └── index.ts            # Public exports
│   └── package.json
├── hooks/                       # Hooks layer
│   ├── src/
│   │   ├── atomic/             # Single-purpose hooks
│   │   ├── composite/          # Screen-level hooks
│   │   ├── utils/              # Hook utilities
│   │   └── index.ts            # Public exports
│   └── package.json
└── ui-components/               # UI layer
    ├── src/
    │   ├── components/         # Reusable components
    │   ├── screens/            # Screen templates
    │   ├── styles/             # Shared styles
    │   └── index.ts            # Public exports
    └── package.json
```

## Migration Strategy

### Phase 1: Service Layer

- Extract existing API calls into service classes
- Implement caching and error handling
- Add retry mechanisms and logging

### Phase 2: Atomic Hooks

- Convert existing data fetching to atomic hooks
- Standardize loading and error states
- Add refetch capabilities

### Phase 3: Composite Hooks

- Group related atomic hooks into screen-level composites
- Implement unified refresh and error handling
- Optimize data dependencies

### Phase 4: UI Components

- Extract presentation logic into pure components
- Remove data fetching from UI components
- Standardize component interfaces

### Phase 5: Screen Refactoring

- Update screens to use composite hooks
- Simplify screen logic and focus on UX
- Implement proper navigation patterns

## Common Patterns

### Loading States

```typescript
// Atomic hook pattern
const { data, loading, error } = useData(id);

// Composite hook pattern
const { data, loading, error, refresh } = useScreenData(params);

// Component pattern
if (loading) return <LoadingSkeleton />;
```

### Error Handling

```typescript
// Service level
try {
  const result = await api.call();
  return { data: result, error: null };
} catch (error) {
  return { data: null, error: error.message };
}

// Hook level
if (error) {
  setState(prev => ({ ...prev, error: error.message }));
}

// Component level
if (error) return <ErrorBoundary error={error} onRetry={refetch} />;
```

### Caching Strategy

```typescript
// Service level caching
class DataService {
  private cache = new Map<string, CacheEntry>();

  async getData(id: string, ttl = 300000) {
    // 5 min default
    const cached = this.getCachedData(id, ttl);
    if (cached) return cached;

    const fresh = await this.fetchFreshData(id);
    this.setCachedData(id, fresh);
    return fresh;
  }
}
```

This architecture provides a robust foundation for building scalable, maintainable, and testable
mobile applications across any domain or use case! 🚀
